#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终版本：导出当日所有销售出库明细单
明确标注所有字段含义，让用户选择合适的字段作为货品编号
"""

import json
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient

def export_final_with_clear_fields():
    """最终版本：导出当日所有销售出库明细单，明确字段含义"""
    
    print("🎯 最终版本：导出当日所有销售出库明细单（明确字段含义）")
    print("=" * 70)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 时间范围: {start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")
    
    # 先获取总数
    print(f"\n📊 正在获取数据总数...")
    try:
        first_response = client.call_api('stockout.query', {
            "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "page_no": 0,
            "page_size": 1
        })
        total_records = first_response.get('total', 0)
        # 确保total_records是整数
        if isinstance(total_records, str):
            try:
                total_records = int(total_records)
            except:
                total_records = 500
        print(f"📈 API返回总记录数: {total_records}")
    except Exception as e:
        print(f"❌ 获取总数失败: {e}")
        total_records = 500  # 使用一个较大的默认值
    
    # 使用更大的页面大小和更多的页数来确保获取所有数据
    page_size = 30  # 使用较小的页面大小，因为API似乎每页只返回30条
    max_pages = (total_records // page_size) + 10  # 多查询更多页确保完整
    
    print(f"📑 预计需要查询 {max_pages} 页数据（每页{page_size}条）")
    
    all_stockouts = []
    unique_stockouts = {}  # 用于去重
    
    # 查询参数
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": page_size
    }
    
    page_no = 0
    consecutive_empty_pages = 0  # 连续空页计数
    
    while page_no < max_pages and consecutive_empty_pages < 5:
        print(f"\n📄 正在获取第 {page_no + 1} 页数据...")
        
        try:
            # 当前页参数
            current_params = {
                **base_params,
                "page_no": page_no
            }
            
            # 调用API
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                print(f"❌ 第 {page_no + 1} 页响应为空")
                consecutive_empty_pages += 1
                page_no += 1
                continue
            
            # 获取数据
            content = response.get('content', [])
            total = response.get('total', 0)
            
            print(f"📊 第 {page_no + 1} 页: API返回 total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
            
            # 处理数据
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
                if not isinstance(page_data, list):
                    page_data = [page_data] if page_data else []
            
            if not page_data:
                print(f"📝 第 {page_no + 1} 页无数据")
                consecutive_empty_pages += 1
                page_no += 1
                continue
            
            # 重置连续空页计数
            consecutive_empty_pages = 0
            
            # 处理所有数据（不仅仅是已发货的）并去重
            all_count = 0
            shipped_count = 0
            for item in page_data:
                if isinstance(item, dict):
                    stockout_no = item.get('stockout_no', '')
                    if stockout_no and stockout_no not in unique_stockouts:
                        unique_stockouts[stockout_no] = item
                        all_count += 1
                        
                        status = str(item.get('status', ''))
                        if status == '95':  # 已发货状态
                            shipped_count += 1
            
            if all_count > 0:
                print(f"✅ 第 {page_no + 1} 页: {all_count} 条新记录, 其中已发货 {shipped_count} 条")
                print(f"📈 累计记录: {len(unique_stockouts)} 条")
            
            page_no += 1
                
        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            page_no += 1
            continue
    
    # 筛选已发货的订单
    all_records = list(unique_stockouts.values())
    shipped_stockouts = [item for item in all_records if str(item.get('status', '')) == '95']
    
    print(f"\n📊 数据获取完成！")
    print(f"📊 总记录数: {len(all_records)} 条")
    print(f"📊 已发货订单: {len(shipped_stockouts)} 条")
    
    if not shipped_stockouts:
        print("📝 今天没有已发货的订单")
        return
    
    # 导出到Excel
    filename = f"最终版_明确字段_销售出库明细单_{today.strftime('%Y%m%d')}.xlsx"
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 设置边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 创建主表（出库单汇总）
        ws_main = wb.active
        ws_main.title = "出库单汇总"
        
        # 主表字段
        main_headers = [
            '出库单号', '原始订单号', '交易号', '原始交易号', '店铺名称', '仓库名称',
            '物流公司', '物流单号', '买家昵称', '收件人姓名', '收件人电话',
            '收件人地址', '订单金额', '商品数量', '商品种类数', '重量',
            '发货时间', '状态', '备注', '客服备注'
        ]
        
        # 写入主表表头
        for col, header in enumerate(main_headers, 1):
            cell = ws_main.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = thin_border
        
        # 写入主表数据
        for row_idx, stockout in enumerate(shipped_stockouts, 2):
            row_data = [
                stockout.get('stockout_no', ''),
                stockout.get('src_order_no', ''),
                stockout.get('trade_no', ''),
                stockout.get('src_tids', ''),
                stockout.get('shop_name', ''),
                stockout.get('warehouse_name', ''),
                stockout.get('logistics_name', ''),
                stockout.get('logistics_no', ''),
                stockout.get('buyer_nick', ''),
                stockout.get('receiver_name', ''),
                stockout.get('receiver_mobile', ''),
                # 拼接完整地址
                ' '.join([
                    stockout.get('receiver_province', ''),
                    stockout.get('receiver_city', ''),
                    stockout.get('receiver_district', ''),
                    stockout.get('receiver_address', '')
                ]).strip(),
                stockout.get('total_amount', ''),
                stockout.get('goods_count', ''),
                stockout.get('goods_type_count', ''),
                stockout.get('calc_weight', ''),  # 使用calc_weight字段
                stockout.get('consign_time', ''),
                '已发货',
                stockout.get('remark', ''),
                stockout.get('cs_remark', '')
            ]
            
            for col_idx, value in enumerate(row_data, 1):
                cell = ws_main.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border
                cell.alignment = Alignment(vertical="center")
        
        # 自动调整主表列宽
        for column in ws_main.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_main.column_dimensions[column_letter].width = adjusted_width
        
        # 创建商品明细表
        ws_detail = wb.create_sheet("商品明细")
        
        # 商品明细表字段（明确标注字段含义）
        detail_headers = [
            '出库单号', '原始订单号', 
            'spec_no(规格编号-可作为货品编号)', 'barcode(条码-可作为货品编号)', 
            'goods_name(商品名称)', 'spec_name(规格名称)',
            'num(商品数量)', 'order_price(单价)', 'goods_total_amount(总金额)', 
            'gross_weight(重量)', 'remark(备注)'
        ]
        
        # 写入明细表表头
        for col, header in enumerate(detail_headers, 1):
            cell = ws_detail.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = thin_border
        
        # 写入商品明细数据
        detail_row = 2
        total_detail_count = 0
        
        for stockout in shipped_stockouts:
            stockout_no = stockout.get('stockout_no', '')
            src_order_no = stockout.get('src_order_no', '')
            
            # 获取商品明细
            goods_detail = stockout.get('goods_detail', [])
            if isinstance(goods_detail, list) and goods_detail:
                for detail in goods_detail:
                    detail_data = [
                        stockout_no,
                        src_order_no,
                        detail.get('spec_no', ''),           # 规格编号（可作为货品编号）
                        detail.get('barcode', ''),           # 条码（可作为货品编号）
                        detail.get('goods_name', ''),        # 商品名称
                        detail.get('spec_name', ''),         # 规格名称
                        detail.get('num', ''),               # 商品数量
                        detail.get('order_price', ''),       # 单价
                        detail.get('goods_total_amount', ''), # 总金额
                        detail.get('gross_weight', ''),      # 重量
                        detail.get('remark', '')             # 备注
                    ]
                    
                    for col_idx, value in enumerate(detail_data, 1):
                        cell = ws_detail.cell(row=detail_row, column=col_idx, value=value)
                        cell.border = thin_border
                        cell.alignment = Alignment(vertical="center")
                    
                    detail_row += 1
                    total_detail_count += 1
            else:
                # 如果没有明细，至少记录一行基本信息
                no_detail_data = [stockout_no, src_order_no, '无明细数据', '', '', '', '', '', '', '', '']
                for col_idx, value in enumerate(no_detail_data, 1):
                    cell = ws_detail.cell(row=detail_row, column=col_idx, value=value)
                    cell.border = thin_border
                    cell.alignment = Alignment(vertical="center")
                detail_row += 1
        
        # 自动调整明细表列宽
        for column in ws_detail.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_detail.column_dimensions[column_letter].width = adjusted_width
        
        # 创建字段说明表
        ws_fields = wb.create_sheet("字段说明")
        
        # 字段说明数据
        field_explanations = [
            ['字段名', '含义', '说明'],
            ['spec_no', '规格编号', '商品SKU的唯一标识，在旺店通WMS中可作为货品编号使用'],
            ['barcode', '条码', '商品的条码，如果您的业务中条码就是货品编号，可使用此字段'],
            ['goods_name', '商品名称', '商品的名称'],
            ['spec_name', '规格名称', '商品规格的名称'],
            ['num', '商品数量', '出库的商品数量'],
            ['order_price', '单价', '商品的单价'],
            ['goods_total_amount', '总金额', '该商品的总金额'],
            ['gross_weight', '重量', '商品的重量'],
            ['remark', '备注', '商品的备注信息'],
            ['', '', ''],
            ['重要说明', '', ''],
            ['货品编号问题', '在旺店通WMS的stockout.query接口中', '没有传统意义上的goods_no字段'],
            ['推荐方案1', '使用spec_no作为货品编号', 'spec_no是商品SKU的唯一标识'],
            ['推荐方案2', '使用barcode作为货品编号', '如果您的业务中条码就是货品编号'],
            ['其他方案', '联系旺店通技术支持', '确认是否有其他接口可获取goods_no字段']
        ]
        
        for row_idx, row_data in enumerate(field_explanations, 1):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws_fields.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border
                if row_idx == 1 or row_idx == 12:  # 表头和重要说明
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                else:
                    cell.alignment = Alignment(vertical="center")
        
        # 调整字段说明表列宽
        ws_fields.column_dimensions['A'].width = 20
        ws_fields.column_dimensions['B'].width = 30
        ws_fields.column_dimensions['C'].width = 50
        
        # 创建统计汇总表
        ws_summary = wb.create_sheet("统计汇总")
        
        # 统计数据
        total_amount = sum(float(s.get('total_amount', 0) or 0) for s in shipped_stockouts)
        total_goods = sum(float(s.get('goods_count', 0) or 0) for s in shipped_stockouts)
        
        # 物流公司统计
        logistics_stats = {}
        warehouse_stats = {}
        shop_stats = {}
        
        for stockout in shipped_stockouts:
            logistics = stockout.get('logistics_name', '未知')
            logistics_stats[logistics] = logistics_stats.get(logistics, 0) + 1
            
            warehouse = stockout.get('warehouse_name', '未知')
            warehouse_stats[warehouse] = warehouse_stats.get(warehouse, 0) + 1
            
            shop = stockout.get('shop_name', '未知')
            shop_stats[shop] = shop_stats.get(shop, 0) + 1
        
        # 写入统计数据
        summary_data = [
            ['统计项目', '数值', '说明'],
            ['查询日期', today.strftime('%Y-%m-%d'), '数据查询的日期'],
            ['导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '文件生成时间'],
            ['API总记录数', total_records, 'API返回的总记录数'],
            ['实际获取记录数', len(all_records), '实际获取的总记录数'],
            ['已发货订单总数', len(shipped_stockouts), '状态为95的出库单数量'],
            ['商品明细总数', total_detail_count, '所有商品明细条数'],
            ['订单总金额', f'{total_amount:.2f}', '所有订单金额汇总'],
            ['商品总数量', f'{total_goods:.0f}', '所有商品数量汇总'],
            ['主要物流公司', max(logistics_stats.items(), key=lambda x: x[1])[0] if logistics_stats else '无', '使用最多的物流公司'],
            ['主要发货仓库', max(warehouse_stats.items(), key=lambda x: x[1])[0] if warehouse_stats else '无', '发货最多的仓库'],
            ['主要店铺', max(shop_stats.items(), key=lambda x: x[1])[0] if shop_stats else '无', '订单最多的店铺']
        ]
        
        for row_idx, row_data in enumerate(summary_data, 1):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws_summary.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border
                if row_idx == 1:  # 表头
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                else:
                    cell.alignment = Alignment(vertical="center")
        
        # 调整统计表列宽
        ws_summary.column_dimensions['A'].width = 20
        ws_summary.column_dimensions['B'].width = 20
        ws_summary.column_dimensions['C'].width = 30
        
        # 保存文件
        wb.save(filename)
        
        print(f"✅ 导出成功！")
        print(f"📊 API总记录数: {total_records}")
        print(f"📊 实际获取记录数: {len(all_records)}")
        print(f"📊 已发货订单: {len(shipped_stockouts)} 条")
        print(f"📋 商品明细: {total_detail_count} 条")
        print(f"💰 订单总金额: {total_amount:.2f}")
        print(f"📦 商品总数量: {total_goods:.0f}")
        print(f"📁 文件路径: {filename}")
        
        print(f"\n🎉 最终版销售出库明细单导出完成！")
        print(f"\n📝 关于货品编号的重要说明：")
        print(f"   ❌ 旺店通WMS的stockout.query接口中没有goods_no字段")
        print(f"   ✅ 推荐使用 spec_no（规格编号）作为货品编号")
        print(f"   ✅ 或者使用 barcode（条码）作为货品编号")
        print(f"   📋 详细说明请查看Excel文件中的'字段说明'工作表")
        
    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    export_final_with_clear_fields()
