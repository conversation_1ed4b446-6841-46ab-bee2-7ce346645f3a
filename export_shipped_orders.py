#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导出今天已发货的销售订单
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from wdt_post_client import WDTPostClient

def export_shipped_orders_today():
    """导出今天已发货的销售订单"""
    
    print("🚚 导出今天已发货的销售订单")
    print("=" * 50)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 获取今天的时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    # 如果是今天，结束时间不能超过当前时间
    current_time = datetime.now()
    if end_time > current_time:
        end_time = current_time
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 首先测试不同的状态代码，找到已发货的状态
    print(f"\n🔍 测试不同的订单状态...")
    
    # API允许的订单状态代码（需要测试确认哪个是已发货）
    test_statuses = [
        (5, "已取消"),
        (30, "待审核"),
        (95, "可能的已发货状态")
    ]
    
    valid_statuses = []
    
    for status_code, status_desc in test_statuses:
        try:
            print(f"   测试状态 {status_code} ({status_desc})...")
            
            response = client.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                trade_status=status_code,
                page_no=0,
                page_size=1
            )
            
            total = response.get('total', 0)
            if total > 0:
                print(f"   ✅ 状态 {status_code}: 找到 {total} 条订单")
                valid_statuses.append((status_code, status_desc, total))
            else:
                print(f"   📝 状态 {status_code}: 无数据")
                
        except Exception as e:
            error_msg = str(e)
            if "时间参数错误" in error_msg:
                print(f"   ⚠️ 状态 {status_code}: 时间参数错误")
            elif "Invalid json" in error_msg:
                print(f"   ❌ 状态 {status_code}: 无效状态或权限问题")
            else:
                print(f"   ❌ 状态 {status_code}: {error_msg}")
    
    print(f"\n📊 找到有数据的状态:")
    if valid_statuses:
        for status_code, status_desc, total in valid_statuses:
            print(f"   状态 {status_code}: {total} 条订单")
    else:
        print("   没有找到有数据的状态")
    
    # 如果找到了有效状态，让用户选择或自动选择最可能的已发货状态
    if valid_statuses:
        print(f"\n🎯 开始导出订单数据...")
        
        all_orders = []
        
        # 对每个有效状态进行详细查询
        for status_code, status_desc, total in valid_statuses:
            print(f"\n🔍 详细查询状态 {status_code} ({status_desc})...")
            
            try:
                page_no = 0
                page_size = 100
                status_orders = []
                
                # 先获取第一页数据确定总记录数
                print("   🔍 正在获取数据总数...")
                first_page = client.query_sales_trades(
                    start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    trade_status=status_code,
                    page_no=0,
                    page_size=1
                )
                
                if not first_page or 'content' not in first_page:
                    print("   ❌ 无法获取数据总数")
                    continue
                    
                total_orders = first_page.get('total', 0)
                if total_orders == 0:
                    print("   📝 没有可用的订单数据")
                    continue
                    
                print(f"   📊 总共需要获取 {total_orders} 条订单")
                
                # 计算需要的页数
                page_size = 100
                total_pages = (total_orders + page_size - 1) // page_size
                print(f"   📑 总共需要查询 {total_pages} 页数据")
                
                # 获取所有页数据
                status_orders = []
                for page in range(total_pages):
                    print(f"   ⏳ 正在获取第 {page + 1}/{total_pages} 页数据...")
                    
                    try:
                        response = client.query_sales_trades(
                            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                            end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                            trade_status=status_code,
                            page_no=page,
                            page_size=page_size
                        )
                        
                        if not response or 'content' not in response:
                            print(f"   ⚠️ 第 {page + 1} 页数据获取失败，跳过")
                            continue
                            
                        trades = response.get('content', [])
                        if trades:
                            # 添加状态信息到每个订单
                            for trade in trades:
                                trade['status_name'] = f"状态{status_code}"
                                trade['status_description'] = status_desc
                            
                            status_orders.extend(trades)
                            print(f"   ✅ 成功获取第 {page + 1} 页 {len(trades)} 条订单")
                        else:
                            print(f"   📝 第 {page + 1} 页没有数据")
                            
                        # 显示进度
                        progress = len(status_orders) / total_orders * 100
                        print(f"   📈 当前进度: {len(status_orders)}/{total_orders} ({progress:.1f}%)")
                        
                    except Exception as e:
                        print(f"   ❌ 第 {page + 1} 页查询异常: {str(e)}")
                        continue
                
                print(f"   ✅ 状态 {status_code} 总计: {len(status_orders)} 条订单")
                
                # 显示订单样例来帮助判断是否为已发货状态
                if status_orders:
                    print("\n🔍 订单数据结构示例:")
                    print(json.dumps(status_orders[0], indent=2, ensure_ascii=False))
                    print(f"   📋 订单样例:")
                    sample_order = status_orders[0]
                    print(f"      ERP单号: {sample_order.get('trade_no', 'N/A')}")
                    print(f"      订单状态: {sample_order.get('trade_status', 'N/A')}")
                    print(f"      接单时间: {sample_order.get('trade_create_time', 'N/A')}")
                    print(f"      物流编码: {sample_order.get('logistics_code', 'N/A')}")
                    
                    # 检查是否有物流相关信息（已发货订单通常有物流信息）
                    has_logistics = bool(sample_order.get('logistics_code'))
                    if has_logistics:
                        print(f"      🚚 包含物流信息，可能是已发货状态")
                    else:
                        print(f"      📝 无物流信息")
                
                all_orders.extend(status_orders)
                
            except Exception as e:
                print(f"   ❌ 查询状态 {status_code} 失败: {e}")
        
        print(f"\n📈 总计获取订单: {len(all_orders)} 条")
        
        if all_orders:
            # 导出到Excel
            filename = f"已发货订单_{today.strftime('%Y%m%d')}.xlsx"
            
            try:
                from openpyxl import Workbook
                from openpyxl.styles import Font, PatternFill, Alignment
                
                print(f"\n📊 开始导出到Excel...")
                print(f"📁 文件名: {filename}")
                
                # 创建工作簿
                wb = Workbook()
                
                # 创建订单明细表
                ws_detail = wb.active
                ws_detail.title = "已发货订单明细"
                
                # 定义表头
                headers = [
                    'ERP单号', '原始单号', '仓储单号', '订单状态', '状态代码', '状态描述',
                    '货主编号', '仓库编号', '店铺名称', '店铺编号', '物流编码',
                    '买家昵称', '收件人省', '收件人市', '收件人区', '收件人地址',
                    '订单总金额', '货品数量', '货品种类数', '预估重量', '订单体积',
                    '客服备注', '买家留言', '平台下单时间', '付款时间', '接单时间',
                    '平台名称', '订单类型', '发货条件', 'COD金额', '标记名称',
                    '商品序号', '商品编码', '商品数量', '商品重量', '商品体积',
                    '商品备注', '行号'
                ]
                
                # 写入表头
                for col, header in enumerate(headers, 1):
                    cell = ws_detail.cell(row=1, column=col, value=header)
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")
                
                # 写入数据
                row = 2
                for order in all_orders:
                    # 基础订单信息
                    base_data = [
                        order.get('trade_no', ''),
                        order.get('src_tids', ''),
                        order.get('src_order_no', ''),
                        order.get('status_name', ''),
                        order.get('trade_status', ''),
                        order.get('status_description', ''),
                        order.get('owner_no', ''),
                        order.get('warehouse_no', ''),
                        order.get('shop_name', ''),
                        order.get('shop_no', ''),
                        order.get('logistics_code', ''),
                        order.get('buyer_nick', ''),
                        order.get('receiver_province', ''),
                        order.get('receiver_city', ''),
                        order.get('receiver_district', ''),
                        order.get('receiver_area', ''),
                        order.get('total_amount', ''),
                        order.get('goods_count', ''),
                        order.get('goods_type_count', ''),
                        order.get('weight', ''),
                        order.get('volume', ''),
                        order.get('cs_remark', ''),
                        order.get('buyer_message', ''),
                        order.get('trade_time', ''),
                        order.get('pay_time', ''),
                        order.get('trade_create_time', ''),
                        order.get('platform_name', ''),
                        get_trade_type_name(order.get('trade_type', 0)),
                        get_delivery_term_name(order.get('delivery_term', 1)),
                        order.get('cod_amount', ''),
                        order.get('flag_name', '')
                    ]
                    
                    # 商品明细
                    goods_detail = order.get('goods_detail', [])
                    if goods_detail:
                        for i, goods in enumerate(goods_detail):
                            goods_data = [
                                i + 1,  # 商品序号
                                goods.get('spec_no', ''),
                                goods.get('num', ''),
                                goods.get('weight', ''),
                                goods.get('volume', ''),
                                goods.get('remark', ''),
                                goods.get('orderline_no', '')
                            ]
                            
                            # 写入完整行数据
                            full_row_data = base_data + goods_data
                            for col, value in enumerate(full_row_data, 1):
                                ws_detail.cell(row=row, column=col, value=value)
                            row += 1
                    else:
                        # 没有商品明细的订单
                        goods_data = ['', '', '', '', '', '', '']
                        full_row_data = base_data + goods_data
                        for col, value in enumerate(full_row_data, 1):
                            ws_detail.cell(row=row, column=col, value=value)
                        row += 1
                
                # 自动调整列宽
                for column in ws_detail.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    ws_detail.column_dimensions[column_letter].width = adjusted_width
                
                # 创建汇总表
                ws_summary = wb.create_sheet("订单汇总")
                
                # 汇总表头
                summary_headers = ['订单状态', '状态代码', '订单数量', '总金额', '总货品数']
                for col, header in enumerate(summary_headers, 1):
                    cell = ws_summary.cell(row=1, column=col, value=header)
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")
                
                # 计算汇总数据
                status_summary = {}
                total_amount = 0
                total_goods_count = 0
                
                for order in all_orders:
                    status_key = f"{order.get('status_name', '未知')}({order.get('trade_status', '')})"
                    if status_key not in status_summary:
                        status_summary[status_key] = {
                            '订单数量': 0,
                            '总金额': 0,
                            '总货品数': 0,
                            '状态代码': order.get('trade_status', '')
                        }
                    
                    status_summary[status_key]['订单数量'] += 1
                    
                    # 处理金额
                    amount = order.get('total_amount', 0)
                    if isinstance(amount, str):
                        try:
                            amount = float(amount)
                        except:
                            amount = 0
                    status_summary[status_key]['总金额'] += amount
                    total_amount += amount
                    
                    # 处理货品数量
                    goods_count = order.get('goods_count', 0)
                    if isinstance(goods_count, str):
                        try:
                            goods_count = float(goods_count)
                        except:
                            goods_count = 0
                    status_summary[status_key]['总货品数'] += goods_count
                    total_goods_count += goods_count
                
                # 写入汇总数据
                summary_row = 2
                for status, data in status_summary.items():
                    ws_summary.cell(row=summary_row, column=1, value=status)
                    ws_summary.cell(row=summary_row, column=2, value=data['状态代码'])
                    ws_summary.cell(row=summary_row, column=3, value=data['订单数量'])
                    ws_summary.cell(row=summary_row, column=4, value=round(data['总金额'], 2))
                    ws_summary.cell(row=summary_row, column=5, value=round(data['总货品数'], 2))
                    summary_row += 1
                
                # 添加总计行
                ws_summary.cell(row=summary_row, column=1, value='总计')
                ws_summary.cell(row=summary_row, column=2, value='所有状态')
                ws_summary.cell(row=summary_row, column=3, value=len(all_orders))
                ws_summary.cell(row=summary_row, column=4, value=round(total_amount, 2))
                ws_summary.cell(row=summary_row, column=5, value=round(total_goods_count, 2))
                
                # 保存文件
                wb.save(filename)
                
                print(f"✅ 导出成功！")
                print(f"📊 总计导出: {len(all_orders)} 条订单")
                print(f"📁 文件路径: {filename}")
                
                # 显示汇总信息
                print(f"\n📊 订单汇总:")
                for status, data in status_summary.items():
                    print(f"   {status}: {data['订单数量']} 条，总金额: {data['总金额']:.2f}")
                
                print(f"\n🎉 已发货订单导出完成！")
                print(f"💡 请检查导出的Excel文件，确认哪些状态是真正的已发货订单")
                
            except ImportError:
                print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
            except Exception as e:
                print(f"❌ 导出失败: {e}")
        else:
            print("📝 今天没有找到订单数据")
    else:
        print("\n📝 今天没有找到任何有效的订单状态")
        print("💡 建议:")
        print("   1. 检查时间范围是否正确")
        print("   2. 确认今天是否有已发货的订单")
        print("   3. 联系技术支持确认已发货订单的状态代码")

def get_trade_type_name(trade_type: int) -> str:
    """获取订单类型名称"""
    type_map = {
        0: "正常订单",
        1: "换货订单", 
        2: "补发订单"
    }
    return type_map.get(trade_type, f"未知类型({trade_type})")

def get_delivery_term_name(delivery_term: int) -> str:
    """获取发货条件名称"""
    term_map = {
        1: "非货到付款",
        2: "货到付款"
    }
    return term_map.get(delivery_term, f"未知条件({delivery_term})")

if __name__ == "__main__":
    export_shipped_orders_today()
