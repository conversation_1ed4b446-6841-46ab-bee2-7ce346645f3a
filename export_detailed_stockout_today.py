#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导出当日销售出库明细单（包含详细商品明细）
专门用于导出销售出库单的详细商品明细信息
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def export_detailed_stockout_today():
    """导出当日销售出库明细单（包含商品明细）"""
    
    print("📦 导出当日销售出库明细单（含商品明细）")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 时间范围: {start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")
    
    # 查询参数
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 100
    }
    
    print(f"🔍 开始查询已发货的出库单...")
    
    all_stockouts = []
    page_no = 0
    
    while True:
        print(f"\n📄 正在获取第 {page_no + 1} 页数据...")
        
        try:
            # 当前页参数
            current_params = {
                **base_params,
                "page_no": page_no
            }
            
            # 调用API
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                print(f"❌ 第 {page_no + 1} 页响应为空")
                break
            
            # 获取数据
            content = response.get('content', [])
            total = response.get('total', 0)
            
            print(f"📊 API返回: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
            
            # 处理数据
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
                if not isinstance(page_data, list):
                    page_data = [page_data] if page_data else []
            
            if not page_data:
                print(f"📝 第 {page_no + 1} 页无数据")
                break
            
            # 筛选已发货的数据（status=95）
            shipped_data = []
            for item in page_data:
                if isinstance(item, dict):
                    status = item.get('status', '')
                    # 确保status是字符串或数字进行比较
                    if str(status) == '95':  # 已发货状态
                        shipped_data.append(item)
            
            print(f"✅ 第 {page_no + 1} 页: 总数据 {len(page_data)} 条, 已发货 {len(shipped_data)} 条")
            
            if shipped_data:
                all_stockouts.extend(shipped_data)
                print(f"📈 累计已发货订单: {len(all_stockouts)} 条")
            
            # 检查是否还有更多数据
            # 确保total是数字类型
            try:
                total = int(total) if total else 0
            except (ValueError, TypeError):
                total = 0

            # 如果当前页数据少于页面大小，说明已经是最后一页
            if len(page_data) < base_params["page_size"]:
                print(f"🏁 已获取完所有数据 (当前页数据:{len(page_data)}, 页面大小:{base_params['page_size']})")
                break
            
            page_no += 1
            
            # 安全限制：最多查询100页
            if page_no >= 100:
                print(f"⚠️ 已查询100页，停止查询")
                break
                
        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            break
    
    print(f"\n📊 查询完成！总计获取 {len(all_stockouts)} 条已发货订单")
    
    if not all_stockouts:
        print("📝 今天没有已发货的订单")
        return
    
    # 导出到Excel（包含商品明细）
    filename = f"销售出库明细单_含商品明细_{today.strftime('%Y%m%d')}.xlsx"
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 创建主表（出库单汇总）
        ws_main = wb.active
        ws_main.title = "出库单汇总"
        
        # 主表字段
        main_headers = [
            '出库单号', '原始订单号', '交易号', '店铺名称', '仓库名称',
            '物流公司', '物流单号', '买家昵称', '收件人姓名', '收件人电话',
            '收件人地址', '订单金额', '商品数量', '商品种类数', '重量',
            '发货时间', '创建时间', '状态', '备注'
        ]
        
        # 写入主表表头
        for col, header in enumerate(main_headers, 1):
            cell = ws_main.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 写入主表数据
        for row_idx, stockout in enumerate(all_stockouts, 2):
            ws_main.cell(row=row_idx, column=1, value=stockout.get('stockout_no', ''))
            ws_main.cell(row=row_idx, column=2, value=stockout.get('src_order_no', ''))
            ws_main.cell(row=row_idx, column=3, value=stockout.get('trade_no', ''))
            ws_main.cell(row=row_idx, column=4, value=stockout.get('shop_name', ''))
            ws_main.cell(row=row_idx, column=5, value=stockout.get('warehouse_name', ''))
            ws_main.cell(row=row_idx, column=6, value=stockout.get('logistics_name', ''))
            ws_main.cell(row=row_idx, column=7, value=stockout.get('logistics_no', ''))
            ws_main.cell(row=row_idx, column=8, value=stockout.get('buyer_nick', ''))
            ws_main.cell(row=row_idx, column=9, value=stockout.get('receiver_name', ''))
            ws_main.cell(row=row_idx, column=10, value=stockout.get('receiver_mobile', ''))
            
            # 拼接完整地址
            address_parts = [
                stockout.get('receiver_province', ''),
                stockout.get('receiver_city', ''),
                stockout.get('receiver_district', ''),
                stockout.get('receiver_address', '')
            ]
            full_address = ' '.join([part for part in address_parts if part])
            ws_main.cell(row=row_idx, column=11, value=full_address)
            
            ws_main.cell(row=row_idx, column=12, value=stockout.get('total_amount', ''))
            ws_main.cell(row=row_idx, column=13, value=stockout.get('goods_count', ''))
            ws_main.cell(row=row_idx, column=14, value=stockout.get('goods_type_count', ''))
            ws_main.cell(row=row_idx, column=15, value=stockout.get('weight', ''))
            ws_main.cell(row=row_idx, column=16, value=stockout.get('consign_time', ''))
            ws_main.cell(row=row_idx, column=17, value=stockout.get('created', ''))
            ws_main.cell(row=row_idx, column=18, value='已发货')
            ws_main.cell(row=row_idx, column=19, value=stockout.get('remark', ''))
        
        # 自动调整主表列宽
        for column in ws_main.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_main.column_dimensions[column_letter].width = adjusted_width
        
        # 创建商品明细表
        ws_detail = wb.create_sheet("商品明细")
        
        # 商品明细表字段
        detail_headers = [
            '出库单号', '原始订单号', '货品编号', '商品编号', '商品名称', '规格名称',
            '商品数量', '单价', '总金额', '成本价', '重量',
            '条码', '品牌', '分类', '是否赠品', '货品备注'
        ]
        
        # 写入明细表表头
        for col, header in enumerate(detail_headers, 1):
            cell = ws_detail.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 写入商品明细数据
        detail_row = 2
        total_detail_count = 0

        for stockout in all_stockouts:
            stockout_no = stockout.get('stockout_no', '')
            src_order_no = stockout.get('src_order_no', '')

            # 获取商品明细
            goods_detail = stockout.get('goods_detail', [])
            if isinstance(goods_detail, list) and goods_detail:
                # 调试输出：只打印第一条记录的数据结构
                if detail_row == 2:
                    print(f"\n🔍 调试信息 - 第一条商品明细数据结构:")
                    print(json.dumps(goods_detail[0], indent=2, ensure_ascii=False))
                for detail in goods_detail:
                    # 尝试多种可能的备注字段名
                    goods_remark = (detail.get('remark') or
                                  detail.get('goods_remark') or
                                  detail.get('note') or
                                  detail.get('memo') or
                                  detail.get('description') or '')

                    ws_detail.cell(row=detail_row, column=1, value=stockout_no)
                    ws_detail.cell(row=detail_row, column=2, value=src_order_no)
                    ws_detail.cell(row=detail_row, column=3, value=detail.get('spec_no', ''))   # 规格编号（作为货品编号）
                    ws_detail.cell(row=detail_row, column=4, value=detail.get('goods_name', ''))
                    ws_detail.cell(row=detail_row, column=5, value=detail.get('spec_name', ''))
                    ws_detail.cell(row=detail_row, column=6, value=detail.get('num', ''))        # 使用正确的数量字段
                    ws_detail.cell(row=detail_row, column=7, value=detail.get('order_price', '')) # 使用正确的单价字段
                    ws_detail.cell(row=detail_row, column=8, value=detail.get('goods_total_amount', '')) # 使用正确的总金额字段
                    ws_detail.cell(row=detail_row, column=9, value=detail.get('cost_price', ''))
                    ws_detail.cell(row=detail_row, column=10, value=detail.get('gross_weight', '')) # 使用正确的重量字段
                    ws_detail.cell(row=detail_row, column=11, value=detail.get('barcode', ''))
                    ws_detail.cell(row=detail_row, column=12, value=detail.get('brand_name', ''))
                    ws_detail.cell(row=detail_row, column=13, value=detail.get('class_name', ''))
                    ws_detail.cell(row=detail_row, column=14, value='是' if detail.get('gift_type') == 1 else '否')
                    ws_detail.cell(row=detail_row, column=15, value=goods_remark)  # 货品备注
                    detail_row += 1
                    total_detail_count += 1
            else:
                # 如果没有明细，至少记录一行基本信息
                ws_detail.cell(row=detail_row, column=1, value=stockout_no)
                ws_detail.cell(row=detail_row, column=2, value=src_order_no)
                ws_detail.cell(row=detail_row, column=3, value='无明细数据')
                for col in range(4, 16):  # 填充其他列
                    ws_detail.cell(row=detail_row, column=col, value='')
                detail_row += 1
        
        # 自动调整明细表列宽
        for column in ws_detail.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_detail.column_dimensions[column_letter].width = adjusted_width
        
        # 保存文件
        wb.save(filename)
        
        print(f"✅ 导出成功！")
        print(f"📊 总计导出: {len(all_stockouts)} 条已发货订单")
        print(f"📁 文件路径: {filename}")
        print(f"📋 包含两个工作表:")
        print(f"   - 出库单汇总: 订单基本信息")
        print(f"   - 商品明细: 详细商品信息")
        
        print(f"\n🎉 销售出库明细单导出完成！")
        
    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    export_detailed_stockout_today()
