#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试货品档案API，查找货品档案中的备注字段
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def test_goods_archive_api():
    """测试货品档案API"""
    
    print("🔍 测试货品档案API，查找货品档案中的备注字段")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 先获取一些spec_no用于测试
    print("📦 先获取一些商品的spec_no...")
    
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    # 获取一些出库单中的spec_no
    stockout_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 5,
        "page_no": 0
    }
    
    spec_nos = []
    try:
        response = client.call_api('stockout.query', stockout_params)
        if response and 'content' in response:
            content = response.get('content', [])
            if isinstance(content, list):
                for stockout in content:
                    goods_detail = stockout.get('goods_detail', [])
                    if isinstance(goods_detail, list):
                        for detail in goods_detail:
                            spec_no = detail.get('spec_no')
                            if spec_no and spec_no not in spec_nos:
                                spec_nos.append(spec_no)
                                if len(spec_nos) >= 3:  # 只取3个用于测试
                                    break
                    if len(spec_nos) >= 3:
                        break
        
        print(f"✅ 获取到测试用的spec_no: {spec_nos}")
    except Exception as e:
        print(f"❌ 获取spec_no失败: {e}")
        # 使用一些固定的spec_no进行测试
        spec_nos = ["1683214231569040384", "1797735757663568128", "2213159689158853504"]
        print(f"🔄 使用固定的spec_no进行测试: {spec_nos}")
    
    # 尝试不同的货品档案API
    goods_apis = [
        'goods.query',
        'spec.query', 
        'goods.spec.query',
        'goods.archive.query',
        'product.query',
        'item.query',
        'goods.info.query',
        'spec.info.query'
    ]
    
    print(f"\n🔍 尝试不同的货品档案API...")
    
    for api_name in goods_apis:
        print(f"\n📋 测试API: {api_name}")
        
        try:
            # 尝试不同的查询方式
            test_params = [
                {"page_size": 10, "page_no": 0},  # 基础查询
                {"spec_no": spec_nos[0]} if spec_nos else {},  # 按spec_no查询
                {"spec_nos": ",".join(spec_nos[:2])} if len(spec_nos) >= 2 else {},  # 批量查询
            ]
            
            for i, params in enumerate(test_params, 1):
                if not params:
                    continue
                    
                print(f"  🔍 尝试参数组合 {i}: {params}")
                
                try:
                    response = client.call_api(api_name, params)
                    
                    if response and 'content' in response:
                        content = response.get('content', [])
                        total = response.get('total', 0)
                        
                        print(f"    ✅ API调用成功: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
                        
                        # 分析返回的数据结构
                        if isinstance(content, list) and content:
                            first_item = content[0]
                            print(f"    📋 第一条数据的字段:")
                            
                            # 查找可能的备注字段
                            remark_fields = []
                            for key, value in first_item.items():
                                if 'remark' in key.lower() or 'note' in key.lower() or 'memo' in key.lower() or 'desc' in key.lower():
                                    remark_fields.append((key, value))
                                    print(f"      🔍 可能的备注字段 {key}: {value}")
                                else:
                                    print(f"        {key}: {value}")
                            
                            if remark_fields:
                                print(f"    ✅ 找到可能的备注字段: {[field[0] for field in remark_fields]}")
                                
                                # 保存成功的API和数据
                                success_filename = f"goods_archive_success_{api_name.replace('.', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                                with open(success_filename, 'w', encoding='utf-8') as f:
                                    json.dump({
                                        'api_name': api_name,
                                        'params': params,
                                        'response': response,
                                        'remark_fields': remark_fields
                                    }, f, ensure_ascii=False, indent=2)
                                
                                print(f"    📁 成功数据已保存到: {success_filename}")
                                return api_name, params, remark_fields
                            else:
                                print(f"    ❌ 未找到备注相关字段")
                        
                        elif isinstance(content, dict):
                            print(f"    📋 返回的是字典格式:")
                            for key, value in content.items():
                                print(f"      {key}: {value}")
                        
                        else:
                            print(f"    ❌ 返回数据格式异常")
                    
                    else:
                        print(f"    ❌ API返回数据为空或格式错误")
                
                except Exception as e:
                    print(f"    ❌ 参数组合 {i} 调用失败: {e}")
        
        except Exception as e:
            print(f"  ❌ API {api_name} 调用异常: {e}")
    
    print(f"\n📝 总结:")
    print(f"所有货品档案API测试完成，未找到可用的API或备注字段")
    print(f"可能的原因:")
    print(f"1. 账号没有货品档案查询权限")
    print(f"2. API接口名称不正确")
    print(f"3. 需要特殊的参数才能获取备注字段")
    print(f"4. 备注字段在其他接口中")
    
    return None, None, None

if __name__ == "__main__":
    test_goods_archive_api()
