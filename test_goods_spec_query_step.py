#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 goods.spec.query.step 接口
获取货品编号和货品备注
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def test_goods_spec_query_step():
    """测试 goods.spec.query.step 接口"""
    
    print("🔍 测试 goods.spec.query.step 接口")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 先获取一些spec_no用于测试
    print("📦 先获取一些商品的spec_no...")
    
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    spec_nos = []
    try:
        response = client.call_api('stockout.query', {
            "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 5,
            "page_no": 0
        })
        
        if response and 'content' in response:
            content = response.get('content', [])
            if isinstance(content, list):
                for stockout in content:
                    goods_detail = stockout.get('goods_detail', [])
                    if isinstance(goods_detail, list):
                        for detail in goods_detail:
                            spec_no = detail.get('spec_no')
                            if spec_no and spec_no not in spec_nos:
                                spec_nos.append(spec_no)
                                if len(spec_nos) >= 3:
                                    break
                    if len(spec_nos) >= 3:
                        break
        
        print(f"✅ 获取到测试用的spec_no: {spec_nos}")
    except Exception as e:
        print(f"❌ 获取spec_no失败: {e}")
        spec_nos = ["1683214231569040384", "2235684791960143488"]
        print(f"🔄 使用固定的spec_no进行测试: {spec_nos}")
    
    # 测试 goods.spec.query.step 接口
    print(f"\n🔍 测试 goods.spec.query.step 接口...")
    
    # 尝试不同的参数组合（添加货主编号）
    # 根据您的账号信息，货主编号可能是 'changhe' 或其他值
    owner_nos = ['changhe', '', '001', 'default']

    test_params = []

    for owner_no in owner_nos:
        # 基础查询
        test_params.append({"owner_no": owner_no, "page_size": 10, "page_no": 0})

        # 按spec_no查询
        if spec_nos:
            test_params.append({"owner_no": owner_no, "spec_no": spec_nos[0]})

        # 批量查询
        if len(spec_nos) >= 2:
            test_params.append({"owner_no": owner_no, "spec_nos": ",".join(spec_nos[:2])})

        # 带其他可能参数
        test_params.append({"owner_no": owner_no, "page_size": 10, "page_no": 0, "status": 1})
        test_params.append({"owner_no": owner_no, "page_size": 10, "page_no": 0, "is_enabled": 1})
    
    successful_params = None
    goods_data = None
    
    for i, params in enumerate(test_params, 1):
        if not params:
            continue
            
        print(f"\n📋 尝试参数组合 {i}: {params}")
        
        try:
            response = client.call_api('goods.spec.query.step', params)
            
            if response and 'content' in response:
                content = response.get('content', [])
                total = response.get('total', 0)
                
                print(f"✅ API调用成功: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
                
                if isinstance(content, list) and content:
                    first_item = content[0]
                    print(f"📋 第一条数据的所有字段:")
                    
                    # 查找货品编号和备注相关字段
                    goods_no_fields = []
                    remark_fields = []
                    
                    for key, value in first_item.items():
                        if 'goods_no' in key.lower() or 'product_no' in key.lower() or 'item_no' in key.lower():
                            goods_no_fields.append((key, value))
                            print(f"  🔍 货品编号字段 {key}: {value}")
                        elif 'remark' in key.lower() or 'note' in key.lower() or 'memo' in key.lower() or 'desc' in key.lower():
                            remark_fields.append((key, value))
                            print(f"  📝 备注字段 {key}: {value}")
                        else:
                            print(f"    {key}: {value}")
                    
                    if goods_no_fields or remark_fields:
                        print(f"\n✅ 找到有用的字段！")
                        print(f"  货品编号字段: {[field[0] for field in goods_no_fields]}")
                        print(f"  备注字段: {[field[0] for field in remark_fields]}")
                        
                        successful_params = params
                        goods_data = content
                        
                        # 保存成功的数据
                        success_filename = f"goods_spec_query_step_success_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                        with open(success_filename, 'w', encoding='utf-8') as f:
                            json.dump({
                                'api_name': 'goods.spec.query.step',
                                'params': params,
                                'response': response,
                                'goods_no_fields': goods_no_fields,
                                'remark_fields': remark_fields
                            }, f, ensure_ascii=False, indent=2)
                        
                        print(f"📁 成功数据已保存到: {success_filename}")
                        break
                    else:
                        print(f"❌ 未找到货品编号或备注字段")
                
                elif isinstance(content, dict):
                    print(f"📋 返回的是字典格式:")
                    for key, value in content.items():
                        print(f"  {key}: {value}")
                
                else:
                    print(f"❌ 返回数据格式异常")
            
            else:
                print(f"❌ API返回数据为空或格式错误")
        
        except Exception as e:
            print(f"❌ 参数组合 {i} 调用失败: {e}")
    
    # 总结结果
    print(f"\n📝 测试结果总结:")
    if successful_params and goods_data:
        print(f"✅ goods.spec.query.step 接口测试成功！")
        print(f"✅ 成功的参数: {successful_params}")
        print(f"✅ 获取到 {len(goods_data)} 条商品数据")
        
        # 分析字段结构
        if goods_data:
            sample_item = goods_data[0]
            print(f"\n📋 字段分析:")
            
            # 查找关键字段
            key_fields = {}
            for key, value in sample_item.items():
                if any(keyword in key.lower() for keyword in ['goods_no', 'spec_no', 'remark', 'note', 'name']):
                    key_fields[key] = value
            
            print(f"🔑 关键字段:")
            for key, value in key_fields.items():
                print(f"  {key}: {value}")
        
        return successful_params, goods_data
    
    else:
        print(f"❌ goods.spec.query.step 接口测试失败")
        print(f"可能的原因:")
        print(f"1. 参数不正确")
        print(f"2. 接口返回数据格式与预期不符")
        print(f"3. 需要特殊的权限或参数")
        
        return None, None

if __name__ == "__main__":
    test_goods_spec_query_step()
