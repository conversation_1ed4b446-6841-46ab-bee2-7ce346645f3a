#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API频率限制器
用于控制API调用频率，避免触发平台限流
"""

import time
import threading
from collections import deque
from typing import Optional
import logging


class RateLimiter:
    """API频率限制器"""
    
    def __init__(self, max_calls: int = 10, time_window: int = 1):
        """
        初始化频率限制器
        
        Args:
            max_calls: 时间窗口内最大调用次数
            time_window: 时间窗口大小（秒）
        """
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = deque()
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"初始化频率限制器: {max_calls} 次/{time_window} 秒")
    
    def acquire(self, timeout: Optional[float] = None) -> bool:
        """
        获取调用许可
        
        Args:
            timeout: 超时时间（秒），None表示无限等待
            
        Returns:
            bool: 是否成功获取许可
        """
        start_time = time.time()
        
        while True:
            with self.lock:
                current_time = time.time()
                
                # 清理过期的调用记录
                while self.calls and current_time - self.calls[0] >= self.time_window:
                    self.calls.popleft()
                
                # 检查是否可以调用
                if len(self.calls) < self.max_calls:
                    self.calls.append(current_time)
                    self.logger.debug(f"获取调用许可成功，当前窗口内调用次数: {len(self.calls)}/{self.max_calls}")
                    return True
                
                # 计算需要等待的时间
                if self.calls:
                    wait_time = self.time_window - (current_time - self.calls[0])
                    if wait_time > 0:
                        self.logger.debug(f"频率限制生效，需要等待 {wait_time:.2f} 秒")
                        
                        # 检查超时
                        if timeout is not None:
                            elapsed = current_time - start_time
                            if elapsed >= timeout:
                                self.logger.warning(f"获取调用许可超时: {timeout} 秒")
                                return False
                            
                            # 调整等待时间，不超过剩余超时时间
                            wait_time = min(wait_time, timeout - elapsed)
                        
                        # 释放锁后等待
                        time.sleep(min(wait_time, 0.1))  # 最多等待0.1秒后重新检查
                    else:
                        # 理论上不应该到这里，但为了安全起见
                        time.sleep(0.01)
                else:
                    # 没有调用记录，直接允许
                    self.calls.append(current_time)
                    return True
    
    def get_status(self) -> dict:
        """
        获取限制器状态
        
        Returns:
            dict: 状态信息
        """
        with self.lock:
            current_time = time.time()
            
            # 清理过期记录
            while self.calls and current_time - self.calls[0] >= self.time_window:
                self.calls.popleft()
            
            return {
                'max_calls': self.max_calls,
                'time_window': self.time_window,
                'current_calls': len(self.calls),
                'remaining_calls': self.max_calls - len(self.calls),
                'window_start': self.calls[0] if self.calls else None,
                'window_end': current_time
            }
    
    def reset(self):
        """重置限制器"""
        with self.lock:
            self.calls.clear()
            self.logger.info("频率限制器已重置")


class BatchProcessor:
    """批量处理器，用于处理大量API调用"""
    
    def __init__(self, rate_limiter: RateLimiter, batch_size: int = 100, batch_delay: float = 0.1):
        """
        初始化批量处理器
        
        Args:
            rate_limiter: 频率限制器
            batch_size: 批量大小
            batch_delay: 批次间延迟（秒）
        """
        self.rate_limiter = rate_limiter
        self.batch_size = batch_size
        self.batch_delay = batch_delay
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"初始化批量处理器: 批量大小={batch_size}, 批次延迟={batch_delay}秒")
    
    def process_batch(self, items: list, process_func, *args, **kwargs) -> list:
        """
        批量处理项目
        
        Args:
            items: 要处理的项目列表
            process_func: 处理函数
            *args, **kwargs: 传递给处理函数的额外参数
            
        Returns:
            list: 处理结果列表
        """
        results = []
        total_batches = (len(items) + self.batch_size - 1) // self.batch_size
        
        self.logger.info(f"开始批量处理: 总项目数={len(items)}, 批次数={total_batches}")
        
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            
            self.logger.info(f"处理批次 {batch_num}/{total_batches}, 项目数: {len(batch)}")
            
            batch_results = []
            for item in batch:
                # 等待频率限制许可
                if not self.rate_limiter.acquire(timeout=30):
                    self.logger.error(f"获取频率限制许可超时，跳过项目: {item}")
                    continue
                
                try:
                    result = process_func(item, *args, **kwargs)
                    batch_results.append(result)
                except Exception as e:
                    self.logger.error(f"处理项目失败: {item}, 错误: {e}")
                    batch_results.append(None)
            
            results.extend(batch_results)
            
            # 批次间延迟
            if i + self.batch_size < len(items):
                self.logger.debug(f"批次间延迟 {self.batch_delay} 秒")
                time.sleep(self.batch_delay)
        
        self.logger.info(f"批量处理完成: 成功处理 {len([r for r in results if r is not None])}/{len(items)} 个项目")
        return results


# 全局频率限制器实例
_global_rate_limiter = None
_global_batch_processor = None


def get_rate_limiter(max_calls: int = 10, time_window: int = 1) -> RateLimiter:
    """获取全局频率限制器实例"""
    global _global_rate_limiter
    if _global_rate_limiter is None:
        _global_rate_limiter = RateLimiter(max_calls, time_window)
    return _global_rate_limiter


def get_batch_processor(rate_limiter: Optional[RateLimiter] = None, 
                       batch_size: int = 100, 
                       batch_delay: float = 0.1) -> BatchProcessor:
    """获取全局批量处理器实例"""
    global _global_batch_processor
    if _global_batch_processor is None:
        if rate_limiter is None:
            rate_limiter = get_rate_limiter()
        _global_batch_processor = BatchProcessor(rate_limiter, batch_size, batch_delay)
    return _global_batch_processor
