"""
旺店通企业版 API签名算法实现
根据官方文档：https://open.wangdian.cn/open/guide?path=guide_signsf
"""
import hashlib
import time
from typing import Dict, Any


class WDTEnterpriseSignature:
    """旺店通企业版 API签名算法类"""
    
    @staticmethod
    def generate_timestamp() -> int:
        """
        生成旺店通企业版API所需的时间戳
        
        Returns:
            标准Unix时间戳（北京时间1970-01-01 08:00:00起至现在的总秒数）
        """
        return int(time.time())
    
    @staticmethod
    def process_param(key: str, value: str) -> str:
        """
        处理单个参数
        
        Args:
            key: 参数键名
            value: 参数值
            
        Returns:
            处理后的参数字符串
        """
        # 1. 计算键名的UTF-8字符长度，保留两位，不够补0
        key_length = len(key.encode('utf-8'))
        key_length_str = f"{key_length:02d}"
        
        # 2. 拼接键名部分：长度-键名
        key_part = f"{key_length_str}-{key}"
        
        # 3. 计算值的UTF-8字符长度，保留四位，不够补0，超过4位保留实际结果
        value_length = len(str(value).encode('utf-8'))
        if value_length <= 9999:
            value_length_str = f"{value_length:04d}"
        else:
            value_length_str = str(value_length)
        
        # 4. 拼接值部分：长度-值
        value_part = f"{value_length_str}-{value}"
        
        # 5. 用冒号拼接键值对
        return f"{key_part}:{value_part}"
    
    @staticmethod
    def build_sign_string(params: Dict[str, Any]) -> str:
        """
        构建签名字符串
        
        Args:
            params: 请求参数字典
            
        Returns:
            签名字符串
        """
        # 1. 对所有请求参数按照键名进行正序排序
        sorted_keys = sorted(params.keys())
        
        # 2. 处理每个键值对
        param_parts = []
        for i, key in enumerate(sorted_keys):
            value = params[key]
            param_str = WDTEnterpriseSignature.process_param(key, value)
            
            # 除了最后一个参数，其他参数后面都要加分号
            if i < len(sorted_keys) - 1:
                param_str += ";"
            
            param_parts.append(param_str)
        
        return "".join(param_parts)
    
    @staticmethod
    def generate_sign(params: Dict[str, Any], app_secret: str) -> str:
        """
        生成签名
        
        Args:
            params: 请求参数（不包含sign和appsecret）
            app_secret: 应用密钥
            
        Returns:
            MD5签名字符串（32位小写）
        """
        # 1. 构建签名字符串
        sign_string = WDTEnterpriseSignature.build_sign_string(params)
        
        # 2. 在签名字符串后面拼接appsecret
        sign_string_with_secret = sign_string + app_secret
        
        # 3. 计算MD5（32位小写）
        md5_hash = hashlib.md5(sign_string_with_secret.encode('utf-8')).hexdigest()
        
        return md5_hash
    
    @staticmethod
    def build_request_params(params: Dict[str, Any], sid: str, appkey: str, app_secret: str) -> Dict[str, Any]:
        """
        构建完整的请求参数
        
        Args:
            params: 业务参数
            sid: 卖家账号
            appkey: 接口账号
            app_secret: 应用密钥
            
        Returns:
            完整的请求参数字典
        """
        # 构建基础参数
        request_params = {
            'sid': sid,
            'appkey': appkey,
            'timestamp': WDTEnterpriseSignature.generate_timestamp(),
        }
        
        # 添加业务参数
        request_params.update(params)
        
        # 生成签名
        sign = WDTEnterpriseSignature.generate_sign(request_params, app_secret)
        request_params['sign'] = sign
        
        return request_params
