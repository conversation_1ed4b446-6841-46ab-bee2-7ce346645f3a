#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通过已知的spec_no查询具体的商品信息
尝试使用 goods.spec.query.step 接口查询特定商品
"""

import json
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient

def query_specific_goods():
    """通过已知的spec_no查询具体的商品信息"""
    
    print("🔍 通过已知的spec_no查询具体的商品信息")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 正确的货主编号
    owner_no = "AJT-XLSWDT"
    print(f"📦 使用货主编号: {owner_no}")
    
    # 获取当前出库单中的spec_no
    print("📦 获取当前出库单中的spec_no...")
    
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    spec_nos = []
    goods_info = []
    
    try:
        response = client.call_api('stockout.query', {
            "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 10,
            "page_no": 0
        })
        
        if response and 'content' in response:
            content = response.get('content', [])
            if isinstance(content, list):
                for stockout in content:
                    goods_detail = stockout.get('goods_detail', [])
                    if isinstance(goods_detail, list):
                        for detail in goods_detail:
                            spec_no = detail.get('spec_no')
                            goods_name = detail.get('goods_name')
                            if spec_no and spec_no not in spec_nos:
                                spec_nos.append(spec_no)
                                goods_info.append({
                                    'spec_no': spec_no,
                                    'goods_name': goods_name,
                                    'stockout_remark': detail.get('remark', '')
                                })
                                if len(spec_nos) >= 5:
                                    break
                    if len(spec_nos) >= 5:
                        break
        
        print(f"✅ 获取到 {len(spec_nos)} 个商品的spec_no")
        for info in goods_info:
            print(f"  📦 {info['spec_no']}: {info['goods_name']}")
    
    except Exception as e:
        print(f"❌ 获取spec_no失败: {e}")
        return
    
    if not spec_nos:
        print("❌ 没有获取到spec_no")
        return
    
    # 尝试使用 goods.spec.query.step 查询特定商品
    print(f"\n🔍 尝试使用 goods.spec.query.step 查询特定商品...")
    
    # 设置一个较大的时间范围来查找商品变更记录
    now = datetime.now()
    time_ranges = [
        # 最近1小时
        (now - timedelta(hours=1), now),
        # 今天早上到现在
        (now.replace(hour=0, minute=0, second=0), now.replace(hour=1, minute=0, second=0)),
        # 昨天的某个时间段
        ((now - timedelta(days=1)).replace(hour=10, minute=0, second=0), 
         (now - timedelta(days=1)).replace(hour=11, minute=0, second=0)),
        # 前天的某个时间段
        ((now - timedelta(days=2)).replace(hour=10, minute=0, second=0), 
         (now - timedelta(days=2)).replace(hour=11, minute=0, second=0)),
    ]
    
    found_goods_data = []
    
    for i, (start_time, end_time) in enumerate(time_ranges, 1):
        print(f"\n📅 时间范围 {i}: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # 尝试查询所有商品变更
            response = client.call_api('goods.spec.query.step', {
                "owner_no": owner_no,
                "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                "page_size": 100,
                "page_no": 0
            })
            
            if response and 'content' in response:
                content = response.get('content', [])
                total = response.get('total', 0)
                
                print(f"✅ API调用成功: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
                
                if isinstance(content, list) and content:
                    print(f"📋 找到 {len(content)} 条商品变更记录")
                    
                    # 分析数据结构
                    first_item = content[0]
                    print(f"📋 第一条记录的字段:")
                    
                    goods_no_fields = []
                    remark_fields = []
                    
                    for key, value in first_item.items():
                        if 'goods_no' in key.lower() or 'product_no' in key.lower():
                            goods_no_fields.append((key, value))
                            print(f"  🔍 货品编号字段 {key}: {value}")
                        elif 'remark' in key.lower() or 'note' in key.lower() or 'memo' in key.lower():
                            remark_fields.append((key, value))
                            print(f"  📝 备注字段 {key}: {value}")
                        elif 'spec' in key.lower() or 'name' in key.lower():
                            print(f"  📦 {key}: {value}")
                        else:
                            print(f"    {key}: {value}")
                    
                    # 检查是否包含我们需要的spec_no
                    matching_goods = []
                    for item in content:
                        item_spec_no = item.get('spec_no', '')
                        if item_spec_no in spec_nos:
                            matching_goods.append(item)
                            print(f"  ✅ 找到匹配的商品: {item_spec_no}")
                    
                    if matching_goods:
                        found_goods_data.extend(matching_goods)
                        print(f"✅ 在时间范围 {i} 中找到 {len(matching_goods)} 个匹配的商品")
                    
                    # 保存所有找到的数据
                    if content:
                        success_filename = f"goods_spec_data_range_{i}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                        with open(success_filename, 'w', encoding='utf-8') as f:
                            json.dump({
                                'time_range': f"{start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')}",
                                'total': total,
                                'goods_no_fields': goods_no_fields,
                                'remark_fields': remark_fields,
                                'matching_goods': matching_goods,
                                'all_data': content
                            }, f, ensure_ascii=False, indent=2)
                        
                        print(f"📁 数据已保存到: {success_filename}")
                
                elif total == 0:
                    print(f"📝 该时间范围内无商品变更")
                
                else:
                    print(f"❌ 返回数据格式异常")
            
            else:
                print(f"❌ API返回数据为空或格式错误")
        
        except Exception as e:
            print(f"❌ 时间范围 {i} 查询失败: {e}")
    
    # 总结结果
    print(f"\n📝 查询结果总结:")
    if found_goods_data:
        print(f"✅ 总共找到 {len(found_goods_data)} 条匹配的商品数据")
        
        # 分析找到的数据
        for goods in found_goods_data:
            spec_no = goods.get('spec_no', '')
            goods_name = goods.get('goods_name', '')
            
            # 查找备注字段
            remark_value = ''
            for key, value in goods.items():
                if 'remark' in key.lower() and value:
                    remark_value = value
                    break
            
            print(f"  📦 {spec_no}: {goods_name}")
            if remark_value:
                print(f"    📝 备注: {remark_value}")
            else:
                print(f"    📝 备注: 无")
        
        # 保存最终结果
        final_filename = f"final_goods_with_remarks_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(final_filename, 'w', encoding='utf-8') as f:
            json.dump({
                'query_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'owner_no': owner_no,
                'total_found': len(found_goods_data),
                'goods_data': found_goods_data,
                'original_spec_nos': spec_nos
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 最终结果已保存到: {final_filename}")
        return found_goods_data
    
    else:
        print(f"❌ 没有找到任何匹配的商品数据")
        print(f"\n💡 可能的原因:")
        print(f"1. goods.spec.query.step 只返回在指定时间内有变更的商品")
        print(f"2. 当前查询的商品在这些时间范围内没有变更")
        print(f"3. 需要扩大时间范围或使用其他接口")
        
        return []

if __name__ == "__main__":
    query_specific_goods()
