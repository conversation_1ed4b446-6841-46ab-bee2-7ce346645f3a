#!/usr/bin/env python3
"""
旺店通企业版WMS销售出库单查询主程序
"""
import argparse
import logging
import json
from datetime import datetime, timedelta, date
import sys
import os

from wdt_enterprise_client import WDTEnterpriseClient, WDTEnterpriseAPIException
from enterprise_sales_stockout import EnterpriseSalesStockoutQuery
from config import WDTConfig


def setup_logging(log_level='INFO'):
    """设置日志"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('wdt_enterprise_api.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )


def parse_date(date_str):
    """解析日期字符串"""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        try:
            return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            raise argparse.ArgumentTypeError(f"无效的日期格式: {date_str}，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS")


def export_to_json(data, filename):
    """导出为JSON格式"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print(f"数据已导出到: {filename}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='旺店通企业版WMS销售出库单查询工具')
    
    # 时间参数
    parser.add_argument('--start-date', type=parse_date, 
                       help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=parse_date,
                       help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--hours', type=int, default=1,
                       help='查询最近N小时的数据（默认1小时）')
    
    # 查询参数
    parser.add_argument('--status', type=int, choices=[5, 55, 95, 105, 110, 113],
                       help='出库单状态: 5=已取消, 55=已审核, 95=已发货, 105=部分打款, 110=已完成, 113=异常发货')
    parser.add_argument('--warehouse-no', type=str,
                       help='仓库编号')
    parser.add_argument('--shop-no', type=str,
                       help='店铺编号')
    
    # 分页参数
    parser.add_argument('--page-size', type=int, default=100,
                       help='每页大小（1-100，默认100）')
    parser.add_argument('--max-pages', type=int,
                       help='最大查询页数（用于限制查询量）')
    
    # 输出参数
    parser.add_argument('--output', type=str,
                       help='输出文件名（JSON格式）')
    parser.add_argument('--details-only', action='store_true',
                       help='只输出简化的详情信息')
    
    # 其他参数
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    parser.add_argument('--segment-hours', type=int, default=24,
                       help='时间分段查询的小时数（默认24小时）')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    try:
        # 验证配置
        config = WDTConfig
        config.validate_config()
        
        print("旺店通企业版WMS销售出库单查询工具")
        print("=" * 60)
        print(f"SID: {config.SID}")
        print(f"APP_KEY: {config.APP_KEY}")
        print()
        
        # 确定查询时间范围
        if args.start_date and args.end_date:
            if isinstance(args.start_date, date):
                start_time = datetime.combine(args.start_date, datetime.min.time())
            else:
                start_time = args.start_date
                
            if isinstance(args.end_date, date):
                end_time = datetime.combine(args.end_date, datetime.max.time())
            else:
                end_time = args.end_date
        else:
            # 使用最近N小时
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=args.hours)
        
        print(f"查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 验证时间跨度
        time_diff = end_time - start_time
        if time_diff.days > 30:
            print("⚠️ 警告: 时间跨度超过30天，将使用分段查询")
            use_segments = True
        else:
            use_segments = False
        
        # 创建查询器
        query = EnterpriseSalesStockoutQuery(config)
        
        # 执行查询
        print("\n开始查询...")
        
        if use_segments:
            # 分段查询
            stockout_list = query.query_by_time_segments(
                start_time=start_time,
                end_time=end_time,
                segment_hours=args.segment_hours,
                status=args.status,
                page_size=args.page_size,
                max_pages=args.max_pages,
                warehouse_no=args.warehouse_no,
                shop_no=args.shop_no
            )
        else:
            # 普通查询
            stockout_list = query.query_all_pages(
                start_time=start_time,
                end_time=end_time,
                status=args.status,
                page_size=args.page_size,
                max_pages=args.max_pages,
                warehouse_no=args.warehouse_no,
                shop_no=args.shop_no
            )
        
        print(f"\n查询完成，共找到 {len(stockout_list)} 条出库单记录")
        
        # 处理输出
        if args.details_only:
            # 只输出简化详情
            output_data = query.get_stockout_details(stockout_list)
        else:
            # 输出完整数据
            output_data = stockout_list
        
        # 输出结果
        if args.output:
            export_to_json(output_data, args.output)
        else:
            # 控制台输出（限制数量）
            display_count = min(len(output_data), 5)
            if display_count > 0:
                print(f"\n显示前 {display_count} 条记录:")
                print(json.dumps(output_data[:display_count], ensure_ascii=False, indent=2))
                
                if len(output_data) > display_count:
                    print(f"\n... 还有 {len(output_data) - display_count} 条记录")
                    print("使用 --output 参数保存完整结果到文件")
            else:
                print("\n未找到符合条件的记录")
        
        print("\n查询完成！")
        
    except WDTEnterpriseAPIException as e:
        logger.error(f"API调用失败: {e}")
        if e.code == 1090:
            print("\n❌ 权限错误:")
            print("请在旺店通开放平台的[应用管理]中申请销售出库单查询接口权限")
            print("文档地址: https://open.wangdian.cn/qyb/open/apidoc")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"\n❌ 执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
