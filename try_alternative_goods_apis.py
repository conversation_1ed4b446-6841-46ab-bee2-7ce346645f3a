#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
尝试其他可能的API来获取货品档案信息
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def try_alternative_goods_apis():
    """尝试其他可能的API来获取货品档案信息"""
    
    print("🔍 尝试其他可能的API来获取货品档案信息")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 获取一些spec_no用于测试
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print("📦 先获取一些商品的spec_no...")
    
    spec_nos = []
    try:
        response = client.call_api('stockout.query', {
            "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 3,
            "page_no": 0
        })
        
        if response and 'content' in response:
            content = response.get('content', [])
            if isinstance(content, list):
                for stockout in content:
                    goods_detail = stockout.get('goods_detail', [])
                    if isinstance(goods_detail, list):
                        for detail in goods_detail:
                            spec_no = detail.get('spec_no')
                            if spec_no and spec_no not in spec_nos:
                                spec_nos.append(spec_no)
                                if len(spec_nos) >= 3:
                                    break
                    if len(spec_nos) >= 3:
                        break
        
        print(f"✅ 获取到测试用的spec_no: {spec_nos}")
    except Exception as e:
        print(f"❌ 获取spec_no失败: {e}")
        spec_nos = ["1986391445790819712", "2013248623728526464", "1683214231569040384"]
        print(f"🔄 使用固定的spec_no进行测试: {spec_nos}")
    
    # 尝试其他可能的API接口
    alternative_apis = [
        # 库存相关API（可能包含商品信息）
        'stock.query',
        'inventory.query',
        'stock.goods.query',
        
        # 采购相关API（可能包含商品档案）
        'purchase.goods.query',
        'supplier.goods.query',
        
        # 销售相关API（可能包含商品信息）
        'sales.goods.query',
        'order.goods.query',
        
        # 基础数据API
        'basic.goods.query',
        'master.goods.query',
        'base.spec.query',
        
        # 可能的其他命名方式
        'wms.goods.query',
        'erp.goods.query',
        'goods.master.query',
        'spec.master.query',
        
        # 尝试不带query的接口
        'goods.get',
        'spec.get',
        'goods.info',
        'spec.info',
        
        # 尝试其他可能的接口
        'goods.list',
        'spec.list',
        'product.list',
        'item.list'
    ]
    
    print(f"\n🔍 尝试其他可能的API接口...")
    
    successful_apis = []
    
    for api_name in alternative_apis:
        print(f"\n📋 测试API: {api_name}")
        
        try:
            # 尝试基础查询
            print(f"  🔍 尝试基础查询...")
            try:
                response = client.call_api(api_name, {
                    "page_size": 5,
                    "page_no": 0
                })
                
                if response and 'content' in response:
                    content = response.get('content', [])
                    total = response.get('total', 0)
                    
                    print(f"    ✅ 基础查询成功: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
                    
                    if isinstance(content, list) and content:
                        first_item = content[0]
                        print(f"    📋 第一条数据的字段:")
                        
                        # 查找备注相关字段
                        remark_fields = []
                        goods_fields = []
                        
                        for key, value in first_item.items():
                            if 'remark' in key.lower() or 'note' in key.lower() or 'memo' in key.lower() or 'desc' in key.lower():
                                remark_fields.append((key, value))
                                print(f"      🔍 备注字段 {key}: {value}")
                            elif 'goods' in key.lower() or 'spec' in key.lower() or 'product' in key.lower():
                                goods_fields.append((key, value))
                                print(f"      📦 商品字段 {key}: {value}")
                            else:
                                print(f"        {key}: {value}")
                        
                        if remark_fields or goods_fields:
                            successful_apis.append({
                                'api_name': api_name,
                                'remark_fields': remark_fields,
                                'goods_fields': goods_fields,
                                'sample_data': first_item
                            })
                            print(f"    ✅ 找到有用的字段！")
                    
                else:
                    print(f"    ❌ 基础查询返回数据为空")
            
            except Exception as e:
                error_msg = str(e)
                if "无该接口" in error_msg or "调用权限" in error_msg:
                    print(f"    ❌ 无权限: {error_msg}")
                elif "未部署" in error_msg or "暂不支持" in error_msg:
                    print(f"    ❌ 不支持: {error_msg}")
                else:
                    print(f"    ❌ 基础查询失败: {e}")
            
            # 如果基础查询失败，尝试带spec_no的查询
            if spec_nos:
                print(f"  🔍 尝试带spec_no的查询...")
                try:
                    response = client.call_api(api_name, {
                        "spec_no": spec_nos[0]
                    })
                    
                    if response and 'content' in response:
                        content = response.get('content', [])
                        print(f"    ✅ spec_no查询成功: content数量={len(content) if isinstance(content, list) else 'N/A'}")
                        
                        if isinstance(content, list) and content:
                            first_item = content[0]
                            # 查找备注字段
                            for key, value in first_item.items():
                                if 'remark' in key.lower() or 'note' in key.lower():
                                    print(f"      🔍 找到备注字段 {key}: {value}")
                    
                except Exception as e:
                    error_msg = str(e)
                    if "无该接口" not in error_msg and "未部署" not in error_msg:
                        print(f"    ❌ spec_no查询失败: {e}")
        
        except Exception as e:
            print(f"  ❌ API {api_name} 调用异常: {e}")
    
    # 总结结果
    print(f"\n📝 测试结果总结:")
    if successful_apis:
        print(f"✅ 找到 {len(successful_apis)} 个可用的API:")
        for api_info in successful_apis:
            print(f"  📋 {api_info['api_name']}:")
            if api_info['remark_fields']:
                print(f"    🔍 备注字段: {[field[0] for field in api_info['remark_fields']]}")
            if api_info['goods_fields']:
                print(f"    📦 商品字段: {[field[0] for field in api_info['goods_fields']]}")
        
        # 保存成功的API信息
        success_filename = f"successful_goods_apis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(success_filename, 'w', encoding='utf-8') as f:
            json.dump(successful_apis, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 成功的API信息已保存到: {success_filename}")
        return successful_apis
    
    else:
        print(f"❌ 没有找到可用的货品档案API")
        print(f"\n💡 可能的解决方案:")
        print(f"1. 联系旺店通技术支持，申请货品档案API权限")
        print(f"2. 确认账号类型是否支持货品档案查询")
        print(f"3. 检查是否有其他方式获取货品备注信息")
        print(f"4. 考虑在WMS系统中手动导出货品档案，然后通过spec_no关联")
        
        return []

if __name__ == "__main__":
    try_alternative_goods_apis()
