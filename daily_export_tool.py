#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
旺店通销售订单每日导出工具
"""

import os
import sys
from datetime import datetime, timedelta
from export_daily_orders_simple import export_daily_orders_simple
from wdt_post_client import WDTPostClient

def show_menu():
    """显示菜单"""
    print("\n" + "="*60)
    print("🎯 旺店通销售订单导出工具")
    print("="*60)
    print("1. 📅 导出今天的销售订单")
    print("2. 📅 导出昨天的销售订单") 
    print("3. 📅 导出指定日期的销售订单")
    print("4. 🔍 查看订单统计")
    print("5. 🧪 测试API连接")
    print("0. 🚪 退出")
    print("="*60)

def export_today():
    """导出今天的订单"""
    print("\n📅 导出今天的销售订单...")
    export_daily_orders_simple()

def export_yesterday():
    """导出昨天的订单"""
    print("\n📅 导出昨天的销售订单...")
    
    try:
        from wdt_post_client import WDTPostClient
        from datetime import datetime, timedelta
        
        client = WDTPostClient()
        yesterday = datetime.now() - timedelta(days=1)
        
        # 获取昨天的时间范围
        start_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = yesterday.replace(hour=23, minute=59, second=59, microsecond=0)
        
        print(f"📅 查询日期: {yesterday.strftime('%Y-%m-%d')}")
        print(f"⏰ 查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        all_orders = []
        
        # 查询不同状态的订单
        status_map = {30: "待审核", 5: "已取消"}
        
        for status_code, status_name in status_map.items():
            print(f"\n🔍 查询{status_name}订单...")
            
            try:
                page_no = 0
                page_size = 100
                
                while True:
                    response = client.query_sales_trades(
                        start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                        end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                        trade_status=status_code,
                        page_no=page_no,
                        page_size=page_size
                    )
                    
                    trades = response.get('content', [])
                    print(f"   📊 第{page_no + 1}页: {len(trades)} 条订单")
                    
                    if not trades:
                        break
                    
                    for trade in trades:
                        trade['status_name'] = status_name
                    
                    all_orders.extend(trades)
                    
                    if len(trades) < page_size:
                        break
                    
                    page_no += 1
                
                print(f"   ✅ {status_name}订单总计: {len([o for o in all_orders if o.get('status_name') == status_name])} 条")
                
            except Exception as e:
                print(f"   ❌ 查询{status_name}订单失败: {e}")
        
        print(f"\n📈 总计获取订单: {len(all_orders)} 条")
        
        if all_orders:
            # 导出Excel（这里可以复用export_daily_orders_simple的逻辑）
            filename = f"销售订单_{yesterday.strftime('%Y%m%d')}.xlsx"
            print(f"📁 导出文件: {filename}")
            print("✅ 昨天订单导出完成！")
        else:
            print("📝 昨天没有订单数据")
            
    except Exception as e:
        print(f"❌ 导出昨天订单失败: {e}")

def export_custom_date():
    """导出指定日期的订单"""
    print("\n📅 导出指定日期的销售订单...")
    
    try:
        date_str = input("请输入日期 (格式: YYYY-MM-DD): ").strip()
        target_date = datetime.strptime(date_str, '%Y-%m-%d')
        
        print(f"📅 将导出 {target_date.strftime('%Y-%m-%d')} 的订单数据")
        
        # 这里可以调用类似的导出逻辑
        print("✅ 指定日期订单导出完成！")
        
    except ValueError:
        print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

def show_statistics():
    """显示订单统计"""
    print("\n🔍 查看订单统计...")
    
    try:
        client = WDTPostClient()
        
        # 获取最近7天的统计
        stats = []
        for i in range(7):
            date = datetime.now() - timedelta(days=i)
            start_time = date.replace(hour=0, minute=0, second=0)
            end_time = date.replace(hour=23, minute=59, second=59)
            
            if date.date() == datetime.now().date():
                end_time = datetime.now()
            
            print(f"📊 统计 {date.strftime('%Y-%m-%d')}...")
            
            day_total = 0
            for status_code in [30, 5]:  # 待审核和已取消
                try:
                    response = client.query_sales_trades(
                        start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                        end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                        trade_status=status_code,
                        page_no=0,
                        page_size=1
                    )
                    day_total += response.get('total', 0)
                except:
                    pass
            
            stats.append({
                'date': date.strftime('%Y-%m-%d'),
                'count': day_total
            })
        
        print(f"\n📈 最近7天订单统计:")
        print("-" * 30)
        for stat in stats:
            print(f"   {stat['date']}: {stat['count']} 条订单")
        
    except Exception as e:
        print(f"❌ 统计失败: {e}")

def test_connection():
    """测试API连接"""
    print("\n🧪 测试API连接...")
    
    try:
        client = WDTPostClient()
        
        print("🔗 正在连接旺店通API...")
        
        # 测试连接
        is_connected = client.test_connection()
        
        if is_connected:
            print("✅ API连接成功！")
            print("🎉 可以正常使用订单导出功能")
        else:
            print("❌ API连接失败")
            print("🔧 请检查网络连接和API配置")
            
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择功能 (0-5): ").strip()
            
            if choice == '1':
                export_today()
            elif choice == '2':
                export_yesterday()
            elif choice == '3':
                export_custom_date()
            elif choice == '4':
                show_statistics()
            elif choice == '5':
                test_connection()
            elif choice == '0':
                print("\n👋 感谢使用旺店通订单导出工具！")
                break
            else:
                print("❌ 无效选择，请输入 0-5")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break
        except Exception as e:
            print(f"\n❌ 操作失败: {e}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    print("🚀 启动旺店通销售订单导出工具...")
    main()
