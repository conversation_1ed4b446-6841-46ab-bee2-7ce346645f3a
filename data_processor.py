"""
数据处理和导出功能
"""
import pandas as pd
import json
import csv
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging
import os


class SalesStockoutProcessor:
    """销售出库单数据处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def flatten_stockout_data(self, stockout_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        展平销售出库单数据结构
        
        Args:
            stockout_list: 销售出库单数据列表
            
        Returns:
            展平后的数据列表
        """
        flattened_data = []
        
        for stockout in stockout_list:
            # 基础出库单信息
            base_info = {
                '出库单ID': stockout.get('stockout_id'),
                '出库单号': stockout.get('order_no'),
                '系统订单编号': stockout.get('src_order_no'),
                '订单编号': stockout.get('trade_no'),
                '原始单号': stockout.get('src_trade_no'),
                '仓库编号': stockout.get('warehouse_no'),
                '仓库名称': stockout.get('warehouse_name'),
                '店铺编号': stockout.get('shop_no'),
                '店铺名称': stockout.get('shop_name'),
                '出库单状态': stockout.get('status'),
                '出库单状态名称': self._get_status_name(stockout.get('status')),
                '发货时间': self._format_timestamp(stockout.get('consign_time')),
                '物流单号': stockout.get('logistics_no'),
                '物流公司名称': stockout.get('logistics_name'),
                '收件人姓名': stockout.get('receiver_name'),
                '收件人手机': stockout.get('receiver_mobile'),
                '收件人地址': stockout.get('receiver_address'),
                '省市区': stockout.get('receiver_area'),
                '客户网名': stockout.get('nick_name'),
                '客户姓名': stockout.get('customer_name'),
                '客户编码': stockout.get('customer_no'),
                '货品数量': stockout.get('goods_count'),
                '货品种类': stockout.get('goods_type_count'),
                '总货款': stockout.get('goods_total_amount'),
                '总成本价': stockout.get('goods_total_cost'),
                '邮费': stockout.get('post_amount'),
                '重量': stockout.get('weight'),
                '业务员编号': stockout.get('salesman_no'),
                '业务员姓名': stockout.get('fullname'),
                '拣货员': stockout.get('picker_name'),
                '验货员': stockout.get('examiner_name'),
                '发货员': stockout.get('consigner_name'),
                '打单员': stockout.get('printer_name'),
                '打包员': stockout.get('packager_name'),
                '出库单备注': stockout.get('remark'),
                '买家留言': stockout.get('buyer_message'),
                '客服备注': stockout.get('cs_remark'),
                '下单时间': self._format_timestamp(stockout.get('trade_time')),
                '支付时间': self._format_timestamp(stockout.get('pay_time')),
                '出库单建单时间': self._format_timestamp(stockout.get('stock_check_time')),
                '最后修改时间': stockout.get('modified'),
                '销售类型': stockout.get('trade_type'),
                '平台ID': stockout.get('platform_id'),
                '退款状态': stockout.get('refund_status'),
                '发货条件': stockout.get('delivery_term'),
                '货到付款金额': stockout.get('cod_amount'),
                '优惠金额': stockout.get('discount'),
                '已付金额': stockout.get('receivable'),
                '分拣波次': stockout.get('picklist_no'),
                '分拣序号': stockout.get('picklist_seq')
            }
            
            # 处理明细数据
            details_list = stockout.get('details_list', [])
            if details_list:
                for detail in details_list:
                    detail_info = base_info.copy()
                    detail_info.update({
                        '明细ID': detail.get('rec_id'),
                        '单品ID': detail.get('spec_id'),
                        '商家编码': detail.get('spec_no'),
                        '货品编号': detail.get('goods_no'),
                        '货品名称': detail.get('goods_name'),
                        '规格名称': detail.get('spec_name'),
                        '规格码': detail.get('spec_code'),
                        '主条码': detail.get('barcode'),
                        '明细货品数量': detail.get('goods_count'),
                        '明细成交价': detail.get('sell_price'),
                        '明细总成本': detail.get('total_amount'),
                        '货品成本': detail.get('cost_price'),
                        '明细总重量': detail.get('weight'),
                        '是否赠品': detail.get('gift_type'),
                        '品牌编号': detail.get('brand_no'),
                        '品牌名称': detail.get('brand_name'),
                        '货品类型': detail.get('goods_type'),
                        '分类名称': detail.get('class_name'),
                        '基本单位名称': detail.get('unit_name'),
                        '明细备注': detail.get('remark'),
                        '原始子订单号': detail.get('src_oid'),
                        '子单原始订单号': detail.get('src_tid'),
                        '平台货品ID': detail.get('api_goods_id'),
                        '平台规格ID': detail.get('api_spec_id'),
                        '平台货品名称': detail.get('api_goods_name')
                    })
                    flattened_data.append(detail_info)
            else:
                # 如果没有明细，只添加基础信息
                flattened_data.append(base_info)
        
        return flattened_data
    
    def _get_status_name(self, status: int) -> str:
        """获取状态名称"""
        status_map = {
            5: '已取消',
            10: '待放回',
            50: '待审核',
            51: '缺货',
            52: '缺货待入库',
            53: 'WMS已接单',
            54: '获取电子面单',
            58: '档口锁定',
            60: '待分配',
            61: '排队中',
            63: '待补货',
            65: '待处理',
            70: '待发货',
            73: '爆款锁定',
            75: '待拣货',
            77: '拣货中',
            79: '已拣货',
            90: '延时发货',
            110: '已完成',
            -1: '未发货'
        }
        return status_map.get(status, f'未知状态({status})')
    
    def _format_timestamp(self, timestamp: Any) -> str:
        """格式化时间戳"""
        if not timestamp:
            return ''
        
        try:
            if isinstance(timestamp, str):
                return timestamp
            elif isinstance(timestamp, (int, float)):
                # 毫秒级时间戳
                if timestamp > 1000000000000:
                    timestamp = timestamp / 1000
                return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        except:
            return str(timestamp)
        
        return str(timestamp)
    
    def to_dataframe(self, stockout_list: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        转换为DataFrame
        
        Args:
            stockout_list: 销售出库单数据列表
            
        Returns:
            DataFrame对象
        """
        flattened_data = self.flatten_stockout_data(stockout_list)
        return pd.DataFrame(flattened_data)
    
    def export_to_excel(self, stockout_list: List[Dict[str, Any]], 
                       filename: str = None) -> str:
        """
        导出到Excel文件
        
        Args:
            stockout_list: 销售出库单数据列表
            filename: 文件名，如果为None则自动生成
            
        Returns:
            导出的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'销售出库单_{timestamp}.xlsx'
        
        df = self.to_dataframe(stockout_list)
        
        # 确保输出目录存在
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='销售出库单', index=False)
        
        self.logger.info(f"数据已导出到: {filepath}")
        return filepath
    
    def export_to_csv(self, stockout_list: List[Dict[str, Any]], 
                     filename: str = None, encoding: str = 'utf-8-sig') -> str:
        """
        导出到CSV文件
        
        Args:
            stockout_list: 销售出库单数据列表
            filename: 文件名，如果为None则自动生成
            encoding: 文件编码
            
        Returns:
            导出的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'销售出库单_{timestamp}.csv'
        
        df = self.to_dataframe(stockout_list)
        
        # 确保输出目录存在
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        df.to_csv(filepath, index=False, encoding=encoding)
        
        self.logger.info(f"数据已导出到: {filepath}")
        return filepath
    
    def export_to_json(self, stockout_list: List[Dict[str, Any]], 
                      filename: str = None, flatten: bool = True) -> str:
        """
        导出到JSON文件
        
        Args:
            stockout_list: 销售出库单数据列表
            filename: 文件名，如果为None则自动生成
            flatten: 是否展平数据结构
            
        Returns:
            导出的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'销售出库单_{timestamp}.json'
        
        # 确保输出目录存在
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        
        if flatten:
            data = self.flatten_stockout_data(stockout_list)
        else:
            data = stockout_list
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"数据已导出到: {filepath}")
        return filepath
    
    def get_summary_statistics(self, stockout_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取汇总统计信息
        
        Args:
            stockout_list: 销售出库单数据列表
            
        Returns:
            统计信息字典
        """
        if not stockout_list:
            return {}
        
        df = self.to_dataframe(stockout_list)
        
        stats = {
            '总出库单数': len(stockout_list),
            '总货品数量': df['货品数量'].sum() if '货品数量' in df.columns else 0,
            '总货款': df['总货款'].sum() if '总货款' in df.columns else 0,
            '总成本': df['总成本价'].sum() if '总成本价' in df.columns else 0,
            '总邮费': df['邮费'].sum() if '邮费' in df.columns else 0,
            '按状态统计': df['出库单状态名称'].value_counts().to_dict() if '出库单状态名称' in df.columns else {},
            '按仓库统计': df['仓库名称'].value_counts().to_dict() if '仓库名称' in df.columns else {},
            '按店铺统计': df['店铺名称'].value_counts().to_dict() if '店铺名称' in df.columns else {}
        }
        
        return stats
