#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
货品档案映射模板
用于手动导入货品档案数据，然后与出库单数据关联
"""

import pandas as pd
from datetime import datetime
from wdt_post_client import WDTPostClient

class GoodsArchiveMapper:
    """货品档案映射器"""
    
    def __init__(self, goods_archive_file=None):
        """
        初始化映射器
        
        Args:
            goods_archive_file: 货品档案Excel文件路径
        """
        self.goods_archive_file = goods_archive_file
        self.goods_mapping = {}
        
        if goods_archive_file:
            self.load_goods_archive()
    
    def load_goods_archive(self):
        """加载货品档案数据"""
        try:
            # 读取Excel文件
            df = pd.read_excel(self.goods_archive_file)
            
            # 假设Excel包含以下列：spec_no, goods_name, remark
            # 请根据实际的Excel结构调整列名
            required_columns = ['spec_no', 'goods_name', 'remark']
            
            for column in required_columns:
                if column not in df.columns:
                    print(f"警告: Excel文件中缺少列 '{column}'")
                    return
            
            # 创建映射字典
            for _, row in df.iterrows():
                spec_no = str(row['spec_no'])
                self.goods_mapping[spec_no] = {
                    'goods_name': row['goods_name'],
                    'remark': row['remark'] if pd.notna(row['remark']) else ''
                }
            
            print(f"✅ 成功加载 {len(self.goods_mapping)} 条货品档案数据")
            
        except Exception as e:
            print(f"❌ 加载货品档案失败: {e}")
    
    def get_goods_remark(self, spec_no):
        """
        根据spec_no获取货品备注
        
        Args:
            spec_no: 规格编号
            
        Returns:
            货品备注
        """
        spec_no = str(spec_no)
        if spec_no in self.goods_mapping:
            return self.goods_mapping[spec_no]['remark']
        else:
            return ''
    
    def export_stockout_with_archive_remark(self):
        """导出包含货品档案备注的出库单"""
        
        print("📦 导出包含货品档案备注的出库单...")
        
        # 获取出库单数据（复用现有逻辑）
        client = WDTPostClient()
        today = datetime.now()
        start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
        
        if end_time > datetime.now():
            end_time = datetime.now()
        
        # 这里可以复用之前的出库单查询逻辑
        # 然后在导出时，使用 self.get_goods_remark(spec_no) 获取备注
        
        print("请参考现有的导出脚本，在商品明细部分添加:")
        print("goods_archive_remark = mapper.get_goods_remark(detail.get('spec_no', ''))")
        
        return "需要集成到现有的导出脚本中"

# 使用示例
if __name__ == "__main__":
    # 1. 从WMS系统导出货品档案Excel文件
    # 2. 确保Excel包含 spec_no, goods_name, remark 列
    # 3. 使用以下代码加载和使用
    
    # mapper = GoodsArchiveMapper("货品档案.xlsx")
    # remark = mapper.get_goods_remark("1683214231569040384")
    # print(f"货品备注: {remark}")
    
    print("📋 使用步骤:")
    print("1. 从旺店通WMS系统导出货品档案Excel")
    print("2. 确保Excel包含 spec_no, goods_name, remark 列")
    print("3. 修改 goods_archive_file 路径")
    print("4. 集成到现有的导出脚本中")
