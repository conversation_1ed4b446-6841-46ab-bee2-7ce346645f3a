#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
尝试通过不同的参数获取goods_no字段
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def try_get_goods_no():
    """尝试通过不同的参数获取goods_no字段"""
    
    print("🔍 尝试通过不同的参数获取goods_no字段")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    
    # 基础查询参数
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 3,
        "page_no": 0
    }
    
    # 尝试不同的参数组合
    param_combinations = [
        # 尝试1: 添加详细信息参数
        {**base_params, "detail": 1},
        {**base_params, "with_detail": 1},
        {**base_params, "include_goods_detail": 1},
        {**base_params, "goods_detail": 1},
        
        # 尝试2: 添加扩展字段参数
        {**base_params, "extend_fields": "goods_no"},
        {**base_params, "fields": "goods_no"},
        {**base_params, "include_fields": "goods_no"},
        
        # 尝试3: 添加完整信息参数
        {**base_params, "full_info": 1},
        {**base_params, "complete": 1},
        {**base_params, "all_fields": 1},
    ]
    
    for i, params in enumerate(param_combinations, 1):
        print(f"\n🔍 尝试 {i}: {params}")
        
        try:
            response = client.call_api('stockout.query', params)
            
            if not response:
                print(f"❌ 响应为空")
                continue
            
            content = response.get('content', [])
            if not isinstance(content, list) or not content:
                print(f"❌ 无数据")
                continue
            
            # 检查第一条数据的商品明细
            first_item = content[0]
            goods_detail = first_item.get('goods_detail', [])
            
            if isinstance(goods_detail, list) and goods_detail:
                first_goods = goods_detail[0]
                if 'goods_no' in first_goods:
                    print(f"✅ 成功！找到 goods_no: {first_goods.get('goods_no')}")
                    print(f"✅ 成功的参数组合: {params}")
                    
                    # 保存成功的参数和数据
                    success_filename = f"success_goods_no_params_{today.strftime('%Y%m%d')}.json"
                    with open(success_filename, 'w', encoding='utf-8') as f:
                        json.dump({
                            'success_params': params,
                            'sample_data': content[0],
                            'goods_no_value': first_goods.get('goods_no')
                        }, f, ensure_ascii=False, indent=2)
                    
                    print(f"📁 成功参数已保存到: {success_filename}")
                    return params  # 返回成功的参数
                else:
                    print(f"❌ 仍然没有 goods_no 字段")
                    print(f"   可用字段: {list(first_goods.keys())}")
            else:
                print(f"❌ 没有商品明细")
                
        except Exception as e:
            print(f"❌ 调用失败: {e}")
    
    print(f"\n📝 所有尝试都失败了，stockout.query接口可能确实不包含goods_no字段")
    
    # 尝试其他可能的接口
    print(f"\n🔍 尝试其他可能的接口...")
    
    # 尝试销售出库单查询接口（如果存在）
    other_apis = [
        'stockout.sales.query',
        'stockout.detail.query', 
        'sales.stockout.query',
        'wms.stockout.query',
        'stockout.goods.query'
    ]
    
    for api in other_apis:
        print(f"\n🔍 尝试接口: {api}")
        try:
            response = client.call_api(api, base_params)
            if response:
                print(f"✅ {api} 接口调用成功")
                content = response.get('content', [])
                if isinstance(content, list) and content:
                    first_item = content[0]
                    goods_detail = first_item.get('goods_detail', [])
                    if isinstance(goods_detail, list) and goods_detail:
                        first_goods = goods_detail[0]
                        if 'goods_no' in first_goods:
                            print(f"✅ 在 {api} 接口中找到 goods_no: {first_goods.get('goods_no')}")
                            return api
                        else:
                            print(f"❌ {api} 接口中也没有 goods_no 字段")
                    else:
                        print(f"❌ {api} 接口没有商品明细")
                else:
                    print(f"❌ {api} 接口返回数据为空")
            else:
                print(f"❌ {api} 接口调用失败")
        except Exception as e:
            print(f"❌ {api} 接口调用异常: {e}")
    
    print(f"\n💡 建议:")
    print(f"1. 联系旺店通技术支持，确认获取goods_no的正确方法")
    print(f"2. 检查是否有其他API接口包含goods_no字段")
    print(f"3. 确认您的账号是否有权限访问包含goods_no的接口")
    print(f"4. 暂时使用spec_no或barcode作为商品标识")
    
    return None

if __name__ == "__main__":
    try_get_goods_no()
