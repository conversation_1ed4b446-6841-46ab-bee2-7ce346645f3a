#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
彻底搜索goods_no字段
重新仔细检查API返回的所有字段，确保没有遗漏goods_no
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def search_goods_no_thoroughly():
    """彻底搜索goods_no字段"""
    
    print("🔍 彻底搜索goods_no字段")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    
    # 查询参数
    params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 10,  # 取10条数据进行详细分析
        "page_no": 0
    }
    
    print(f"🔍 获取样本数据进行彻底的字段搜索...")
    
    try:
        response = client.call_api('stockout.query', params)
        
        if not response:
            print("❌ API响应为空")
            return
        
        content = response.get('content', [])
        total = response.get('total', 0)
        
        print(f"📊 API返回: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
        
        # 处理数据
        page_data = []
        if isinstance(content, list):
            page_data = content
        elif isinstance(content, dict):
            page_data = content.get('content', [])
            if not isinstance(page_data, list):
                page_data = [page_data] if page_data else []
        
        if not page_data:
            print("📝 无数据")
            return
        
        print(f"\n📋 彻底分析前 {len(page_data)} 条数据的所有字段...")
        
        # 收集所有字段名
        all_fields = set()
        goods_detail_fields = set()
        
        for i, item in enumerate(page_data):
            print(f"\n🔍 第 {i+1} 条出库单详细分析:")
            print(f"出库单号: {item.get('stockout_no', 'N/A')}")
            print(f"状态: {item.get('status', 'N/A')}")
            
            # 收集出库单级别的所有字段
            for key in item.keys():
                all_fields.add(key)
            
            # 检查出库单级别是否有goods_no
            if 'goods_no' in item:
                print(f"✅ 在出库单级别找到 goods_no: {item.get('goods_no')}")
            
            # 重点分析商品明细字段
            goods_detail = item.get('goods_detail', [])
            print(f"\n🛍️ 商品明细字段分析:")
            print(f"goods_detail类型: {type(goods_detail)}")
            print(f"goods_detail长度: {len(goods_detail) if isinstance(goods_detail, list) else 'N/A'}")
            
            if isinstance(goods_detail, list) and goods_detail:
                for j, detail in enumerate(goods_detail):
                    print(f"\n  📦 第 {j+1} 个商品的详细字段分析:")
                    
                    # 收集商品明细的所有字段
                    for key in detail.keys():
                        goods_detail_fields.add(key)
                    
                    # 检查是否有goods_no
                    if 'goods_no' in detail:
                        print(f"    ✅ 找到 goods_no: {detail.get('goods_no')}")
                    else:
                        print(f"    ❌ 未找到 goods_no 字段")
                    
                    # 显示所有字段和值
                    print(f"    📋 所有字段:")
                    for key, value in detail.items():
                        # 特别标注可能相关的字段
                        if 'goods' in key.lower() or 'no' in key.lower() or 'code' in key.lower():
                            print(f"      🔍 {key}: {value}")
                        else:
                            print(f"        {key}: {value}")
            
            # 只分析前3条，避免输出过多
            if i >= 2:
                break
        
        # 总结所有字段
        print(f"\n📊 字段总结:")
        print(f"🏢 出库单级别的所有字段 ({len(all_fields)} 个):")
        for field in sorted(all_fields):
            if 'goods' in field.lower() or 'no' in field.lower():
                print(f"  🔍 {field}")
            else:
                print(f"    {field}")
        
        print(f"\n📦 商品明细级别的所有字段 ({len(goods_detail_fields)} 个):")
        for field in sorted(goods_detail_fields):
            if 'goods' in field.lower() or 'no' in field.lower():
                print(f"  🔍 {field}")
            else:
                print(f"    {field}")
        
        # 特别检查是否有goods_no
        print(f"\n🎯 goods_no 字段搜索结果:")
        if 'goods_no' in all_fields:
            print(f"  ✅ 在出库单级别找到 goods_no 字段")
        else:
            print(f"  ❌ 在出库单级别未找到 goods_no 字段")
        
        if 'goods_no' in goods_detail_fields:
            print(f"  ✅ 在商品明细级别找到 goods_no 字段")
        else:
            print(f"  ❌ 在商品明细级别未找到 goods_no 字段")
        
        # 保存完整的字段分析结果
        analysis_filename = f"complete_field_analysis_{today.strftime('%Y%m%d')}.json"
        with open(analysis_filename, 'w', encoding='utf-8') as f:
            json.dump({
                'total': total,
                'sample_count': len(page_data),
                'stockout_level_fields': list(sorted(all_fields)),
                'goods_detail_level_fields': list(sorted(goods_detail_fields)),
                'has_goods_no_in_stockout': 'goods_no' in all_fields,
                'has_goods_no_in_goods_detail': 'goods_no' in goods_detail_fields,
                'sample_data': page_data[:2]  # 保存前2条完整数据
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 完整字段分析结果已保存到: {analysis_filename}")
        
        # 如果确实没有goods_no，提供替代方案
        if 'goods_no' not in all_fields and 'goods_no' not in goods_detail_fields:
            print(f"\n💡 替代方案建议:")
            print(f"由于确实没有找到 goods_no 字段，建议:")
            print(f"1. 使用 spec_no 作为商品标识（规格编号）")
            print(f"2. 使用 barcode 作为商品标识（条码）")
            print(f"3. 联系旺店通技术支持确认是否需要其他API接口")
            print(f"4. 检查是否需要在API调用时添加特定参数来获取 goods_no")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    search_goods_no_thoroughly()
