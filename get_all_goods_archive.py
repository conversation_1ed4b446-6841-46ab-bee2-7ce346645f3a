#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取所有货品档案信息
通过扩大时间范围和多种策略来获取尽可能多的货品编号
"""

import json
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient

class AllGoodsArchiveCollector:
    """全量货品档案收集器"""
    
    def __init__(self):
        self.client = WDTPostClient()
        self.owner_no = "AJT-XLSWDT"
        self.goods_archive = {}
        
    def query_by_extended_time_range(self, days_back=90):
        """通过扩展时间范围查询货品档案"""
        
        print(f"🔍 查询最近 {days_back} 天的货品档案变更信息...")
        
        now = datetime.now()
        total_found = 0
        
        # 按天查询，每天查询多个时间段
        for day in range(days_back):
            query_date = now - timedelta(days=day)
            
            # 每天查询24个小时，每小时一个时间窗口
            for hour in range(24):
                start_time = query_date.replace(hour=hour, minute=0, second=0, microsecond=0)
                end_time = query_date.replace(hour=hour, minute=59, second=59, microsecond=0)
                
                try:
                    response = self.client.call_api('goods.spec.query.step', {
                        "owner_no": self.owner_no,
                        "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "page_size": 100,
                        "page_no": 0
                    })
                    
                    if response and 'content' in response:
                        content = response.get('content', [])
                        if isinstance(content, list) and content:
                            hour_found = 0
                            for item in content:
                                spec_no = item.get('spec_no', '')
                                if spec_no and spec_no not in self.goods_archive:
                                    self.goods_archive[spec_no] = {
                                        'goods_no': item.get('goods_no', ''),
                                        'goods_name': item.get('goods_name', ''),
                                        'spec_name': item.get('spec_name', ''),
                                        'remark': item.get('remark', ''),
                                        'description': item.get('description', ''),
                                        'memo': item.get('memo', ''),
                                        'note': item.get('note', ''),
                                        'barcode': item.get('barcode', ''),
                                        'brand_name': item.get('brand_name', ''),
                                        'update_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                                        'full_data': item
                                    }
                                    hour_found += 1
                            
                            if hour_found > 0:
                                total_found += hour_found
                                print(f"  📅 {query_date.strftime('%Y-%m-%d')} {hour:02d}:00: 找到 {hour_found} 个商品")
                
                except Exception as e:
                    # 忽略错误，继续查询
                    pass
            
            # 每查询完一天，显示进度
            if day % 10 == 0:
                print(f"📊 已查询 {day + 1} 天，累计找到 {len(self.goods_archive)} 个商品")
        
        print(f"\n📊 扩展时间范围查询完成！")
        print(f"📋 总计找到 {len(self.goods_archive)} 个商品的档案信息")
        
        return len(self.goods_archive)
    
    def query_by_spec_no_list(self, spec_no_list):
        """通过已知的spec_no列表查询货品档案"""
        
        print(f"🔍 通过已知的 {len(spec_no_list)} 个spec_no查询货品档案...")
        
        # 为每个spec_no尝试在不同时间范围内查询
        now = datetime.now()
        found_count = 0
        
        for i, spec_no in enumerate(spec_no_list):
            if spec_no in self.goods_archive:
                continue  # 已经有了，跳过
            
            # 尝试多个时间范围
            time_ranges = [
                # 最近7天，每天尝试几个时间点
                (now - timedelta(days=d, hours=h), now - timedelta(days=d, hours=h-1))
                for d in range(7) for h in [1, 6, 12, 18, 23]
            ]
            
            for start_time, end_time in time_ranges:
                try:
                    response = self.client.call_api('goods.spec.query.step', {
                        "owner_no": self.owner_no,
                        "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "page_size": 100,
                        "page_no": 0
                    })
                    
                    if response and 'content' in response:
                        content = response.get('content', [])
                        if isinstance(content, list):
                            for item in content:
                                item_spec_no = item.get('spec_no', '')
                                if item_spec_no == spec_no:
                                    self.goods_archive[spec_no] = {
                                        'goods_no': item.get('goods_no', ''),
                                        'goods_name': item.get('goods_name', ''),
                                        'spec_name': item.get('spec_name', ''),
                                        'remark': item.get('remark', ''),
                                        'description': item.get('description', ''),
                                        'memo': item.get('memo', ''),
                                        'note': item.get('note', ''),
                                        'barcode': item.get('barcode', ''),
                                        'brand_name': item.get('brand_name', ''),
                                        'update_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                                        'full_data': item
                                    }
                                    found_count += 1
                                    print(f"  ✅ 找到 {spec_no}: {item.get('goods_name', '')}")
                                    break
                            
                            if spec_no in self.goods_archive:
                                break  # 找到了，跳出时间循环
                
                except Exception as e:
                    continue
            
            # 显示进度
            if (i + 1) % 50 == 0:
                print(f"📊 已处理 {i + 1}/{len(spec_no_list)} 个spec_no，找到 {found_count} 个")
        
        print(f"📋 通过spec_no查询完成，新找到 {found_count} 个商品")
        return found_count
    
    def get_current_stockout_spec_nos(self):
        """获取当前出库单中的所有spec_no"""
        
        print("📦 获取当前出库单中的所有spec_no...")
        
        today = datetime.now()
        start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
        
        if end_time > datetime.now():
            end_time = datetime.now()
        
        spec_nos = set()
        page_no = 0
        max_pages = 25
        
        while page_no < max_pages:
            try:
                response = self.client.call_api('stockout.query', {
                    "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "page_size": 30,
                    "page_no": page_no
                })
                
                if not response:
                    break
                
                content = response.get('content', [])
                if not isinstance(content, list) or not content:
                    break
                
                for stockout in content:
                    goods_detail = stockout.get('goods_detail', [])
                    if isinstance(goods_detail, list):
                        for detail in goods_detail:
                            spec_no = detail.get('spec_no', '')
                            if spec_no:
                                spec_nos.add(spec_no)
                
                page_no += 1
                
            except Exception as e:
                print(f"❌ 获取出库单第 {page_no + 1} 页失败: {e}")
                break
        
        spec_no_list = list(spec_nos)
        print(f"✅ 获取到 {len(spec_no_list)} 个唯一的spec_no")
        return spec_no_list
    
    def save_archive(self, filename=None):
        """保存货品档案"""
        if not filename:
            filename = f"complete_goods_archive_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.goods_archive, f, ensure_ascii=False, indent=2)
        
        print(f"📁 完整货品档案已保存到: {filename}")
        return filename
    
    def analyze_archive(self):
        """分析货品档案"""
        if not self.goods_archive:
            print("❌ 货品档案为空")
            return
        
        total = len(self.goods_archive)
        goods_no_count = sum(1 for item in self.goods_archive.values() if item.get('goods_no', '').strip())
        remark_count = sum(1 for item in self.goods_archive.values() if item.get('remark', '').strip())
        
        print(f"\n📊 完整货品档案分析:")
        print(f"📦 总商品数: {total}")
        print(f"🔢 有货品编号的商品: {goods_no_count} ({goods_no_count/total*100:.1f}%)")
        print(f"📝 有备注的商品: {remark_count} ({remark_count/total*100:.1f}%)")
        
        # 显示一些示例
        print(f"\n📋 货品编号示例:")
        count = 0
        for spec_no, info in self.goods_archive.items():
            goods_no = info.get('goods_no', '').strip()
            if goods_no and count < 10:
                print(f"  {goods_no}: {info.get('goods_name', '')}")
                count += 1

def main():
    """主函数"""
    print("🔍 获取所有货品档案信息")
    print("=" * 60)
    
    collector = AllGoodsArchiveCollector()
    
    # 策略1: 扩展时间范围查询
    print("\n📅 策略1: 扩展时间范围查询（最近90天）")
    collector.query_by_extended_time_range(days_back=90)
    
    # 策略2: 获取当前出库单中的spec_no并查询
    print("\n📦 策略2: 基于当前出库单的spec_no查询")
    current_spec_nos = collector.get_current_stockout_spec_nos()
    collector.query_by_spec_no_list(current_spec_nos)
    
    # 分析结果
    collector.analyze_archive()
    
    # 保存结果
    archive_file = collector.save_archive()
    
    print(f"\n🎉 完整货品档案收集完成！")
    print(f"📁 档案文件: {archive_file}")
    print(f"📊 总计收集到 {len(collector.goods_archive)} 个商品的档案信息")
    
    return collector

if __name__ == "__main__":
    main()
