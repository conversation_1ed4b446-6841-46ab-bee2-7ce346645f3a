#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终版本：导出包含货品编号和货品备注的销售出库明细单
"""

import json
import os
from datetime import datetime
from wdt_post_client import WDTPostClient

def load_goods_archive():
    """加载货品档案"""
    # 查找最新的货品档案文件
    archive_files = [f for f in os.listdir('.') if f.startswith('goods_archive_') and f.endswith('.json')]
    
    if not archive_files:
        print("❌ 未找到货品档案文件")
        return {}
    
    # 按文件名排序，获取最新的
    latest_file = sorted(archive_files)[-1]
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            goods_archive = json.load(f)
        
        print(f"✅ 成功加载货品档案: {latest_file}")
        print(f"📦 包含 {len(goods_archive)} 个商品的档案信息")
        return goods_archive
        
    except Exception as e:
        print(f"❌ 加载货品档案失败: {e}")
        return {}

def export_final_goods_info():
    """导出最终版本的货品信息"""
    
    print("📦 最终版本：导出包含货品编号和货品备注的销售出库明细单")
    print("=" * 70)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 加载货品档案
    print("📋 加载货品档案...")
    goods_archive = load_goods_archive()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    
    # 获取所有已发货订单
    print(f"\n📊 正在获取所有已发货订单...")
    
    all_stockouts = []
    unique_stockouts = {}
    page_size = 30
    max_pages = 25
    
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": page_size
    }
    
    page_no = 0
    consecutive_empty_pages = 0
    
    while page_no < max_pages and consecutive_empty_pages < 5:
        try:
            current_params = {**base_params, "page_no": page_no}
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                consecutive_empty_pages += 1
                page_no += 1
                continue
            
            content = response.get('content', [])
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
                if not isinstance(page_data, list):
                    page_data = [page_data] if page_data else []
            
            if not page_data:
                consecutive_empty_pages += 1
                page_no += 1
                continue
            
            consecutive_empty_pages = 0
            
            # 处理数据并去重
            shipped_count = 0
            for item in page_data:
                if isinstance(item, dict):
                    stockout_no = item.get('stockout_no', '')
                    if stockout_no and stockout_no not in unique_stockouts:
                        status = str(item.get('status', ''))
                        if status == '95':  # 已发货状态
                            unique_stockouts[stockout_no] = item
                            shipped_count += 1
            
            if shipped_count > 0:
                print(f"📄 第 {page_no + 1} 页: {shipped_count} 条已发货订单")
            
            page_no += 1
                
        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            page_no += 1
            continue
    
    all_stockouts = list(unique_stockouts.values())
    print(f"\n📊 查询完成！总计获取 {len(all_stockouts)} 条已发货订单")
    
    if not all_stockouts:
        print("📝 今天没有已发货的订单")
        return
    
    # 导出到Excel
    filename = f"最终版_货品信息_销售出库明细单_{today.strftime('%Y%m%d')}.xlsx"
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 设置边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 创建商品明细表
        ws_detail = wb.active
        ws_detail.title = "商品明细"
        
        # 商品明细表字段
        detail_headers = [
            '出库单号', '原始订单号', '货品编号(goods_no)', '规格编号(spec_no)', 
            '商品名称', '规格名称', '商品数量', '单价', '总金额', '重量', 
            '条码', '出库备注', '货品档案备注', '数据来源'
        ]
        
        # 写入明细表表头
        for col, header in enumerate(detail_headers, 1):
            cell = ws_detail.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = thin_border
        
        # 写入商品明细数据
        detail_row = 2
        total_detail_count = 0
        stockout_remark_count = 0
        archive_goods_no_count = 0
        archive_remark_count = 0
        archive_match_count = 0
        
        print(f"\n🔍 开始处理商品明细数据...")
        
        for stockout in all_stockouts:
            stockout_no = stockout.get('stockout_no', '')
            src_order_no = stockout.get('src_order_no', '')
            
            # 获取商品明细
            goods_detail = stockout.get('goods_detail', [])
            if isinstance(goods_detail, list) and goods_detail:
                for detail in goods_detail:
                    spec_no = detail.get('spec_no', '')
                    
                    # 获取出库单中的备注
                    stockout_remark = detail.get('remark', '').strip()
                    if stockout_remark:
                        stockout_remark_count += 1
                    
                    # 获取货品档案信息
                    goods_no = ''
                    archive_remark = ''
                    data_source = "出库单"
                    
                    if spec_no in goods_archive:
                        archive_info = goods_archive[spec_no]
                        goods_no = archive_info.get('goods_no', '').strip()
                        archive_remark = (archive_info.get('remark', '').strip() or 
                                        archive_info.get('description', '').strip() or 
                                        archive_info.get('memo', '').strip() or 
                                        archive_info.get('note', '').strip())
                        
                        if goods_no:
                            archive_goods_no_count += 1
                            archive_match_count += 1
                            data_source = "出库单+档案"
                        
                        if archive_remark:
                            archive_remark_count += 1
                    
                    detail_data = [
                        stockout_no,
                        src_order_no,
                        goods_no,                            # 货品编号（来自档案）
                        spec_no,                             # 规格编号
                        detail.get('goods_name', ''),        # 商品名称
                        detail.get('spec_name', ''),         # 规格名称
                        detail.get('num', ''),               # 商品数量
                        detail.get('order_price', ''),       # 单价
                        detail.get('goods_total_amount', ''), # 总金额
                        detail.get('gross_weight', ''),      # 重量
                        detail.get('barcode', ''),           # 条码
                        stockout_remark,                     # 出库备注
                        archive_remark,                      # 货品档案备注
                        data_source                          # 数据来源
                    ]
                    
                    for col_idx, value in enumerate(detail_data, 1):
                        cell = ws_detail.cell(row=detail_row, column=col_idx, value=value)
                        cell.border = thin_border
                        cell.alignment = Alignment(vertical="center")
                        
                        # 高亮显示有数据的重要字段
                        if col_idx == 3 and goods_no:  # 货品编号
                            cell.fill = PatternFill(start_color="E8F5E8", end_color="E8F5E8", fill_type="solid")
                        elif col_idx == 12 and stockout_remark:  # 出库备注
                            cell.fill = PatternFill(start_color="FFFACD", end_color="FFFACD", fill_type="solid")
                        elif col_idx == 13 and archive_remark:  # 货品档案备注
                            cell.fill = PatternFill(start_color="E8F4FD", end_color="E8F4FD", fill_type="solid")
                        elif col_idx == 14 and data_source == "出库单+档案":  # 数据来源
                            cell.fill = PatternFill(start_color="F0F8FF", end_color="F0F8FF", fill_type="solid")
                    
                    detail_row += 1
                    total_detail_count += 1
        
        # 自动调整列宽
        for column in ws_detail.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_detail.column_dimensions[column_letter].width = adjusted_width
        
        # 创建统计汇总表
        ws_summary = wb.create_sheet("统计汇总")
        
        # 统计数据
        total_amount = sum(float(s.get('total_amount', 0) or 0) for s in all_stockouts)
        total_goods = sum(float(s.get('goods_count', 0) or 0) for s in all_stockouts)
        
        # 计算覆盖率
        goods_no_coverage = f"{archive_goods_no_count/total_detail_count*100:.1f}%" if total_detail_count > 0 else "0%"
        archive_match_rate = f"{archive_match_count/total_detail_count*100:.1f}%" if total_detail_count > 0 else "0%"
        stockout_remark_rate = f"{stockout_remark_count/total_detail_count*100:.1f}%" if total_detail_count > 0 else "0%"
        archive_remark_rate = f"{archive_remark_count/total_detail_count*100:.1f}%" if total_detail_count > 0 else "0%"
        
        # 写入统计数据
        summary_data = [
            ['统计项目', '数值', '说明'],
            ['查询日期', today.strftime('%Y-%m-%d'), '数据查询的日期'],
            ['导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '文件生成时间'],
            ['', '', ''],
            ['出库单统计', '', ''],
            ['已发货订单总数', len(all_stockouts), '状态为95的出库单数量'],
            ['商品明细总数', total_detail_count, '所有商品明细条数'],
            ['订单总金额', f'{total_amount:.2f}', '所有订单金额汇总'],
            ['商品总数量', f'{total_goods:.0f}', '所有商品数量汇总'],
            ['', '', ''],
            ['货品档案统计', '', ''],
            ['档案总商品数', len(goods_archive), '档案中包含的商品总数'],
            ['成功匹配档案的商品', archive_match_count, f'匹配率: {archive_match_rate}'],
            ['获得货品编号的商品', archive_goods_no_count, f'覆盖率: {goods_no_coverage}'],
            ['有出库备注的商品', stockout_remark_count, f'覆盖率: {stockout_remark_rate}'],
            ['有档案备注的商品', archive_remark_count, f'覆盖率: {archive_remark_rate}']
        ]
        
        for row_idx, row_data in enumerate(summary_data, 1):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws_summary.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border
                if row_idx == 1 or row_idx in [5, 11]:  # 标题行
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                else:
                    cell.alignment = Alignment(vertical="center")
        
        # 调整统计表列宽
        ws_summary.column_dimensions['A'].width = 25
        ws_summary.column_dimensions['B'].width = 20
        ws_summary.column_dimensions['C'].width = 40
        
        # 保存文件
        wb.save(filename)
        
        print(f"✅ 导出成功！")
        print(f"📊 已发货订单: {len(all_stockouts)} 条")
        print(f"📋 商品明细: {total_detail_count} 条")
        print(f"📦 获得货品编号的商品: {archive_goods_no_count} 条 ({goods_no_coverage})")
        print(f"📝 有出库备注的商品: {stockout_remark_count} 条")
        print(f"📋 有档案备注的商品: {archive_remark_count} 条")
        print(f"🎯 档案匹配率: {archive_match_rate}")
        print(f"💰 订单总金额: {total_amount:.2f}")
        print(f"📦 商品总数量: {total_goods:.0f}")
        print(f"📁 文件路径: {filename}")
        
        print(f"\n🎉 最终版货品信息导出完成！")
        print(f"\n📝 重要成果:")
        print(f"   ✅ 成功获取货品编号: {archive_goods_no_count} 个商品")
        print(f"   ✅ 货品编号来源: goods.spec.query.step 接口")
        print(f"   ✅ 数据完整性: 包含出库单和货品档案的完整信息")
        print(f"   🎨 Excel中用不同颜色标注不同类型的数据")
        
    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    export_final_goods_info()
