#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终完整版：导出包含所有字段的销售出库明细单
包含货品编号、所有可能的备注字段和完整的商品信息
"""

import json
import os
from datetime import datetime
from wdt_post_client import WDTPostClient

def load_complete_goods_archive():
    """加载完整的货品档案"""
    archive_files = [f for f in os.listdir('.') if f.startswith('complete_goods_archive_') and f.endswith('.json')]
    
    if not archive_files:
        print("❌ 未找到完整货品档案文件")
        return {}
    
    latest_file = sorted(archive_files)[-1]
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            goods_archive = json.load(f)
        
        print(f"✅ 成功加载完整货品档案: {latest_file}")
        print(f"📦 包含 {len(goods_archive)} 个商品的档案信息")
        return goods_archive
        
    except Exception as e:
        print(f"❌ 加载完整货品档案失败: {e}")
        return {}

def export_complete_final_with_all_fields():
    """导出包含所有字段的最终完整版"""
    
    print("📦 最终完整版：导出包含所有字段的销售出库明细单")
    print("=" * 80)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 加载完整货品档案
    print("📋 加载完整货品档案...")
    goods_archive = load_complete_goods_archive()
    
    if not goods_archive:
        print("❌ 无法加载货品档案")
        return
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    
    # 获取所有已发货订单
    print(f"\n📊 正在获取所有已发货订单...")
    
    all_stockouts = []
    unique_stockouts = {}
    page_size = 30
    max_pages = 25
    
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": page_size
    }
    
    page_no = 0
    consecutive_empty_pages = 0
    
    while page_no < max_pages and consecutive_empty_pages < 5:
        try:
            current_params = {**base_params, "page_no": page_no}
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                consecutive_empty_pages += 1
                page_no += 1
                continue
            
            content = response.get('content', [])
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
                if not isinstance(page_data, list):
                    page_data = [page_data] if page_data else []
            
            if not page_data:
                consecutive_empty_pages += 1
                page_no += 1
                continue
            
            consecutive_empty_pages = 0
            
            # 处理数据并去重
            shipped_count = 0
            for item in page_data:
                if isinstance(item, dict):
                    stockout_no = item.get('stockout_no', '')
                    if stockout_no and stockout_no not in unique_stockouts:
                        status = str(item.get('status', ''))
                        if status == '95':  # 已发货状态
                            unique_stockouts[stockout_no] = item
                            shipped_count += 1
            
            if shipped_count > 0:
                print(f"📄 第 {page_no + 1} 页: {shipped_count} 条已发货订单")
            
            page_no += 1
                
        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            page_no += 1
            continue
    
    all_stockouts = list(unique_stockouts.values())
    print(f"\n📊 查询完成！总计获取 {len(all_stockouts)} 条已发货订单")
    
    if not all_stockouts:
        print("📝 今天没有已发货的订单")
        return
    
    # 导出到Excel
    filename = f"最终完整版_所有字段_{today.strftime('%Y%m%d')}.xlsx"
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 设置边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 创建商品明细表
        ws_detail = wb.active
        ws_detail.title = "商品明细"
        
        # 扩展的商品明细表字段（包含所有可能的备注和属性字段）
        detail_headers = [
            '出库单号', '原始订单号', '货品编号(goods_no)', '规格编号(spec_no)', 
            '商品名称', '规格名称', '商品数量', '单价', '总金额', '重量', 
            '条码', '品牌', '分类', '出库备注', 
            '货品档案备注(remark)', '短名称(short_name)', 
            '规格属性1(spec_prop1)', '规格属性2(spec_prop2)', '规格属性3(spec_prop3)',
            '规格属性4(spec_prop4)', '规格属性5(spec_prop5)', '规格属性6(spec_prop6)',
            '标志名称(flag_name)', '基本单位', '商品类型', '数据来源', '匹配状态'
        ]
        
        # 写入明细表表头
        for col, header in enumerate(detail_headers, 1):
            cell = ws_detail.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = thin_border
        
        # 写入商品明细数据
        detail_row = 2
        total_detail_count = 0
        archive_match_count = 0
        goods_no_count = 0
        
        print(f"\n🔍 开始处理商品明细数据...")
        
        for stockout in all_stockouts:
            stockout_no = stockout.get('stockout_no', '')
            src_order_no = stockout.get('src_order_no', '')
            
            # 获取商品明细
            goods_detail = stockout.get('goods_detail', [])
            if isinstance(goods_detail, list) and goods_detail:
                for detail in goods_detail:
                    spec_no = detail.get('spec_no', '')
                    
                    # 获取出库单中的备注
                    stockout_remark = detail.get('remark', '').strip()
                    
                    # 获取货品档案信息
                    goods_no = ''
                    archive_remark = ''
                    short_name = ''
                    spec_prop1 = ''
                    spec_prop2 = ''
                    spec_prop3 = ''
                    spec_prop4 = ''
                    spec_prop5 = ''
                    spec_prop6 = ''
                    flag_name = ''
                    base_unit = ''
                    goods_type = ''
                    data_source = "仅出库单"
                    match_status = "未匹配"
                    
                    if spec_no in goods_archive:
                        archive_info = goods_archive[spec_no]
                        full_data = archive_info.get('full_data', {})
                        
                        goods_no = archive_info.get('goods_no', '').strip()
                        archive_remark = full_data.get('remark', '').strip()
                        short_name = full_data.get('short_name', '').strip()
                        spec_prop1 = full_data.get('spec_prop1', '').strip()
                        spec_prop2 = full_data.get('spec_prop2', '').strip()
                        spec_prop3 = full_data.get('spec_prop3', '').strip()
                        spec_prop4 = full_data.get('spec_prop4', '').strip()
                        spec_prop5 = full_data.get('spec_prop5', '').strip()
                        spec_prop6 = full_data.get('spec_prop6', '').strip()
                        flag_name = full_data.get('flag_name', '').strip()
                        base_unit = full_data.get('base_unit', '').strip()
                        goods_type = full_data.get('goods_type', '').strip()
                        
                        archive_match_count += 1
                        match_status = "已匹配档案"
                        data_source = "出库单+档案"
                        
                        if goods_no:
                            goods_no_count += 1
                            match_status = "完美匹配"
                    
                    detail_data = [
                        stockout_no,
                        src_order_no,
                        goods_no,                            # 货品编号
                        spec_no,                             # 规格编号
                        detail.get('goods_name', ''),        # 商品名称
                        detail.get('spec_name', ''),         # 规格名称
                        detail.get('num', ''),               # 商品数量
                        detail.get('order_price', ''),       # 单价
                        detail.get('goods_total_amount', ''), # 总金额
                        detail.get('gross_weight', ''),      # 重量
                        detail.get('barcode', ''),           # 条码
                        detail.get('brand_name', ''),        # 品牌
                        detail.get('class_name', ''),        # 分类
                        stockout_remark,                     # 出库备注
                        archive_remark,                      # 货品档案备注
                        short_name,                          # 短名称
                        spec_prop1,                          # 规格属性1
                        spec_prop2,                          # 规格属性2
                        spec_prop3,                          # 规格属性3
                        spec_prop4,                          # 规格属性4
                        spec_prop5,                          # 规格属性5
                        spec_prop6,                          # 规格属性6
                        flag_name,                           # 标志名称
                        base_unit,                           # 基本单位
                        goods_type,                          # 商品类型
                        data_source,                         # 数据来源
                        match_status                         # 匹配状态
                    ]
                    
                    for col_idx, value in enumerate(detail_data, 1):
                        cell = ws_detail.cell(row=detail_row, column=col_idx, value=value)
                        cell.border = thin_border
                        cell.alignment = Alignment(vertical="center")
                        
                        # 高亮显示有数据的重要字段
                        if col_idx == 3 and goods_no:  # 货品编号
                            cell.fill = PatternFill(start_color="E8F5E8", end_color="E8F5E8", fill_type="solid")
                        elif col_idx == 14 and stockout_remark:  # 出库备注
                            cell.fill = PatternFill(start_color="FFFACD", end_color="FFFACD", fill_type="solid")
                        elif col_idx == 15 and archive_remark:  # 货品档案备注
                            cell.fill = PatternFill(start_color="E8F4FD", end_color="E8F4FD", fill_type="solid")
                        elif col_idx in [16, 17, 18, 19, 20, 21, 22] and value:  # 其他属性字段
                            cell.fill = PatternFill(start_color="F0F8FF", end_color="F0F8FF", fill_type="solid")
                        elif col_idx == 27:  # 匹配状态
                            if match_status == "完美匹配":
                                cell.fill = PatternFill(start_color="D4EDDA", end_color="D4EDDA", fill_type="solid")
                            elif match_status == "已匹配档案":
                                cell.fill = PatternFill(start_color="FFF3CD", end_color="FFF3CD", fill_type="solid")
                            else:
                                cell.fill = PatternFill(start_color="F8D7DA", end_color="F8D7DA", fill_type="solid")
                    
                    detail_row += 1
                    total_detail_count += 1
        
        # 自动调整列宽
        for column in ws_detail.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_detail.column_dimensions[column_letter].width = adjusted_width
        
        # 创建字段说明表
        ws_fields = wb.create_sheet("字段说明")
        
        field_explanations = [
            ['字段名称', '说明', '数据来源'],
            ['货品编号(goods_no)', '旺店通WMS系统中的货品编号', 'goods.spec.query.step接口'],
            ['规格编号(spec_no)', '商品规格的唯一标识', '出库单接口'],
            ['出库备注', '出库单中的备注信息', '出库单接口'],
            ['货品档案备注(remark)', '货品档案中的备注字段', '货品档案接口'],
            ['短名称(short_name)', '商品的简短名称', '货品档案接口'],
            ['规格属性1-6', '商品的规格属性信息', '货品档案接口'],
            ['标志名称(flag_name)', '商品的标志或标签', '货品档案接口'],
            ['基本单位', '商品的计量单位', '货品档案接口'],
            ['商品类型', '商品的分类类型', '货品档案接口'],
            ['匹配状态', '数据匹配情况', '系统计算'],
            ['', '', ''],
            ['重要说明', '', ''],
            ['1. 货品编号覆盖率', f'{goods_no_count/total_detail_count*100:.1f}%', '成功获取货品编号的比例'],
            ['2. 档案匹配率', f'{archive_match_count/total_detail_count*100:.1f}%', '成功匹配货品档案的比例'],
            ['3. 备注字段状态', '所有货品档案的remark字段都为空', '可能业务中未使用此字段'],
            ['4. 其他属性字段', '大部分spec_prop和其他字段也为空', '可能业务中未使用这些字段']
        ]
        
        for row_idx, row_data in enumerate(field_explanations, 1):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws_fields.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border
                if row_idx == 1 or row_idx == 13:  # 表头
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                else:
                    cell.alignment = Alignment(vertical="center")
        
        # 调整字段说明表列宽
        ws_fields.column_dimensions['A'].width = 25
        ws_fields.column_dimensions['B'].width = 40
        ws_fields.column_dimensions['C'].width = 25
        
        # 保存文件
        wb.save(filename)
        
        # 统计数据
        total_amount = sum(float(s.get('total_amount', 0) or 0) for s in all_stockouts)
        total_goods = sum(float(s.get('goods_count', 0) or 0) for s in all_stockouts)
        
        print(f"✅ 导出成功！")
        print(f"📊 已发货订单: {len(all_stockouts)} 条")
        print(f"📋 商品明细: {total_detail_count} 条")
        print(f"📦 获得货品编号的商品: {goods_no_count} 条 ({goods_no_count/total_detail_count*100:.1f}%)")
        print(f"🎯 档案匹配: {archive_match_count} 条 ({archive_match_count/total_detail_count*100:.1f}%)")
        print(f"💰 订单总金额: ¥{total_amount:.2f}")
        print(f"📦 商品总数量: {total_goods:.0f}")
        print(f"📁 文件路径: {filename}")
        
        print(f"\n🎉 最终完整版导出完成！")
        print(f"\n📝 关于货品备注的重要发现:")
        print(f"   ❌ 货品档案中的remark字段确实存在，但所有商品都为空")
        print(f"   ❌ 其他可能的备注字段（short_name, spec_prop1-6等）也都为空")
        print(f"   💡 这表明您的WMS系统中可能确实没有为商品设置备注信息")
        print(f"   💡 或者备注信息可能存储在其他地方（如商品描述、其他系统等）")
        print(f"   ✅ 但我们成功获取了货品编号，覆盖率达到 {goods_no_count/total_detail_count*100:.1f}%")
        
    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    export_complete_final_with_all_fields()
