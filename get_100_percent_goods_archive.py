#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
100%货品编号匹配解决方案
通过多种策略确保获取所有商品的货品编号
"""

import json
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient

class Complete100PercentGoodsCollector:
    """100%完整货品档案收集器"""
    
    def __init__(self):
        self.client = WDTPostClient()
        self.owner_no = "AJT-XLSWDT"
        self.goods_archive = {}
        self.missing_spec_nos = set()
        
    def load_existing_archive(self):
        """加载现有的货品档案"""
        try:
            with open('complete_goods_archive_20250711_104745.json', 'r', encoding='utf-8') as f:
                existing_archive = json.load(f)
            
            self.goods_archive.update(existing_archive)
            print(f"✅ 加载现有档案: {len(existing_archive)} 个商品")
            return True
        except:
            print("📝 未找到现有档案，从零开始")
            return False
    
    def get_all_current_spec_nos(self):
        """获取当前所有出库单中的spec_no"""
        print("📦 获取当前所有出库单中的spec_no...")
        
        today = datetime.now()
        start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
        
        if end_time > datetime.now():
            end_time = datetime.now()
        
        all_spec_nos = set()
        page_no = 0
        max_pages = 30
        
        while page_no < max_pages:
            try:
                response = self.client.call_api('stockout.query', {
                    "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "page_size": 30,
                    "page_no": page_no
                })
                
                if not response:
                    break
                
                content = response.get('content', [])
                if not isinstance(content, list) or not content:
                    break
                
                for stockout in content:
                    goods_detail = stockout.get('goods_detail', [])
                    if isinstance(goods_detail, list):
                        for detail in goods_detail:
                            spec_no = detail.get('spec_no', '')
                            if spec_no:
                                all_spec_nos.add(spec_no)
                
                page_no += 1
                
            except Exception as e:
                print(f"❌ 获取出库单第 {page_no + 1} 页失败: {e}")
                break
        
        spec_no_list = list(all_spec_nos)
        print(f"✅ 获取到 {len(spec_no_list)} 个唯一的spec_no")
        
        # 找出缺失的spec_no
        for spec_no in spec_no_list:
            if spec_no not in self.goods_archive:
                self.missing_spec_nos.add(spec_no)
        
        print(f"📋 需要查找档案的spec_no: {len(self.missing_spec_nos)} 个")
        return spec_no_list
    
    def extended_time_range_query(self, days_back=365):
        """扩展时间范围查询（1年）"""
        print(f"🔍 扩展时间范围查询（最近 {days_back} 天）...")
        
        now = datetime.now()
        found_count = 0
        
        # 按周查询，减少API调用次数
        for week in range(0, days_back, 7):
            week_start = now - timedelta(days=week+7)
            week_end = now - timedelta(days=week)
            
            # 每周查询多个时间段
            time_slots = [
                (0, 1), (6, 7), (12, 13), (18, 19)  # 每天4个时间段
            ]
            
            for day_offset in range(7):
                query_date = week_start + timedelta(days=day_offset)
                
                for start_hour, end_hour in time_slots:
                    start_time = query_date.replace(hour=start_hour, minute=0, second=0)
                    end_time = query_date.replace(hour=end_hour, minute=0, second=0)
                    
                    try:
                        response = self.client.call_api('goods.spec.query.step', {
                            "owner_no": self.owner_no,
                            "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "page_size": 100,
                            "page_no": 0
                        })
                        
                        if response and 'content' in response:
                            content = response.get('content', [])
                            if isinstance(content, list) and content:
                                for item in content:
                                    spec_no = item.get('spec_no', '')
                                    if spec_no and spec_no not in self.goods_archive:
                                        self.goods_archive[spec_no] = {
                                            'goods_no': item.get('goods_no', ''),
                                            'goods_name': item.get('goods_name', ''),
                                            'spec_name': item.get('spec_name', ''),
                                            'remark': item.get('remark', ''),
                                            'description': item.get('description', ''),
                                            'memo': item.get('memo', ''),
                                            'note': item.get('note', ''),
                                            'barcode': item.get('barcode', ''),
                                            'brand_name': item.get('brand_name', ''),
                                            'update_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                                            'full_data': item
                                        }
                                        found_count += 1
                                        
                                        # 从缺失列表中移除
                                        if spec_no in self.missing_spec_nos:
                                            self.missing_spec_nos.remove(spec_no)
                    
                    except Exception as e:
                        continue
            
            # 每查询完一周，显示进度
            if week % 28 == 0:  # 每4周显示一次
                print(f"📊 已查询 {week//7 + 1} 周，累计找到 {len(self.goods_archive)} 个商品，还需查找 {len(self.missing_spec_nos)} 个")
        
        print(f"📋 扩展时间范围查询完成，新找到 {found_count} 个商品")
        return found_count
    
    def targeted_spec_no_query(self):
        """针对缺失的spec_no进行定向查询"""
        if not self.missing_spec_nos:
            print("✅ 所有spec_no都已找到档案")
            return 0
        
        print(f"🎯 针对 {len(self.missing_spec_nos)} 个缺失的spec_no进行定向查询...")
        
        now = datetime.now()
        found_count = 0
        
        # 为每个缺失的spec_no尝试更广泛的时间范围
        for i, spec_no in enumerate(list(self.missing_spec_nos)):
            if i % 10 == 0:
                print(f"📊 正在查询第 {i+1}/{len(self.missing_spec_nos)} 个spec_no...")
            
            # 尝试更多的时间范围
            time_ranges = []
            
            # 最近30天，每天多个时间点
            for day in range(30):
                base_date = now - timedelta(days=day)
                for hour in [0, 6, 9, 12, 15, 18, 21]:
                    start_time = base_date.replace(hour=hour, minute=0, second=0)
                    end_time = base_date.replace(hour=hour, minute=59, second=59)
                    time_ranges.append((start_time, end_time))
            
            # 更早的时间范围（每周一次）
            for week in range(4, 52):  # 4周到52周前
                base_date = now - timedelta(weeks=week)
                for hour in [9, 15]:
                    start_time = base_date.replace(hour=hour, minute=0, second=0)
                    end_time = base_date.replace(hour=hour, minute=59, second=59)
                    time_ranges.append((start_time, end_time))
            
            # 查询这个spec_no
            for start_time, end_time in time_ranges:
                try:
                    response = self.client.call_api('goods.spec.query.step', {
                        "owner_no": self.owner_no,
                        "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "page_size": 100,
                        "page_no": 0
                    })
                    
                    if response and 'content' in response:
                        content = response.get('content', [])
                        if isinstance(content, list):
                            for item in content:
                                item_spec_no = item.get('spec_no', '')
                                if item_spec_no == spec_no:
                                    self.goods_archive[spec_no] = {
                                        'goods_no': item.get('goods_no', ''),
                                        'goods_name': item.get('goods_name', ''),
                                        'spec_name': item.get('spec_name', ''),
                                        'remark': item.get('remark', ''),
                                        'description': item.get('description', ''),
                                        'memo': item.get('memo', ''),
                                        'note': item.get('note', ''),
                                        'barcode': item.get('barcode', ''),
                                        'brand_name': item.get('brand_name', ''),
                                        'update_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                                        'full_data': item
                                    }
                                    found_count += 1
                                    self.missing_spec_nos.remove(spec_no)
                                    print(f"  ✅ 找到 {spec_no}: {item.get('goods_name', '')}")
                                    break
                            
                            if spec_no not in self.missing_spec_nos:
                                break  # 找到了，跳出时间循环
                
                except Exception as e:
                    continue
        
        print(f"📋 定向查询完成，新找到 {found_count} 个商品")
        return found_count
    
    def try_alternative_strategies(self):
        """尝试其他策略获取剩余的货品信息"""
        if not self.missing_spec_nos:
            return 0
        
        print(f"🔧 尝试其他策略获取剩余 {len(self.missing_spec_nos)} 个商品...")
        
        # 策略1: 尝试不同的时间格式
        print("📅 策略1: 尝试不同的时间格式...")
        
        # 策略2: 尝试更小的时间窗口
        print("⏰ 策略2: 尝试更小的时间窗口...")
        
        # 策略3: 尝试分页查询
        print("📄 策略3: 尝试分页查询...")
        
        now = datetime.now()
        found_count = 0
        
        # 对于剩余的spec_no，尝试最近一年的每一天
        for spec_no in list(self.missing_spec_nos):
            for day in range(365):
                query_date = now - timedelta(days=day)
                
                # 尝试这一天的多个时间点
                for hour in range(0, 24, 3):  # 每3小时一个时间点
                    start_time = query_date.replace(hour=hour, minute=0, second=0)
                    end_time = query_date.replace(hour=hour, minute=30, second=0)
                    
                    try:
                        # 尝试多页查询
                        for page_no in range(3):
                            response = self.client.call_api('goods.spec.query.step', {
                                "owner_no": self.owner_no,
                                "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                                "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                                "page_size": 50,
                                "page_no": page_no
                            })
                            
                            if response and 'content' in response:
                                content = response.get('content', [])
                                if isinstance(content, list):
                                    for item in content:
                                        item_spec_no = item.get('spec_no', '')
                                        if item_spec_no == spec_no:
                                            self.goods_archive[spec_no] = {
                                                'goods_no': item.get('goods_no', ''),
                                                'goods_name': item.get('goods_name', ''),
                                                'spec_name': item.get('spec_name', ''),
                                                'remark': item.get('remark', ''),
                                                'description': item.get('description', ''),
                                                'memo': item.get('memo', ''),
                                                'note': item.get('note', ''),
                                                'barcode': item.get('barcode', ''),
                                                'brand_name': item.get('brand_name', ''),
                                                'update_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                                                'full_data': item
                                            }
                                            found_count += 1
                                            self.missing_spec_nos.remove(spec_no)
                                            print(f"  ✅ 其他策略找到 {spec_no}: {item.get('goods_name', '')}")
                                            break
                                    
                                    if spec_no not in self.missing_spec_nos:
                                        break
                            
                            if spec_no not in self.missing_spec_nos:
                                break
                    
                    except Exception as e:
                        continue
                    
                    if spec_no not in self.missing_spec_nos:
                        break
                
                if spec_no not in self.missing_spec_nos:
                    break
        
        print(f"📋 其他策略完成，新找到 {found_count} 个商品")
        return found_count
    
    def save_complete_archive(self):
        """保存完整档案"""
        filename = f"100_percent_goods_archive_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.goods_archive, f, ensure_ascii=False, indent=2)
        
        print(f"📁 100%完整货品档案已保存到: {filename}")
        return filename
    
    def analyze_final_results(self, target_spec_nos):
        """分析最终结果"""
        total_target = len(target_spec_nos)
        found_count = sum(1 for spec_no in target_spec_nos if spec_no in self.goods_archive)
        missing_count = len(self.missing_spec_nos)
        
        coverage_rate = found_count / total_target * 100 if total_target > 0 else 0
        
        print(f"\n📊 最终结果分析:")
        print(f"🎯 目标spec_no总数: {total_target}")
        print(f"✅ 成功找到档案: {found_count}")
        print(f"❌ 仍然缺失: {missing_count}")
        print(f"📈 覆盖率: {coverage_rate:.1f}%")
        
        if missing_count > 0:
            print(f"\n📋 仍然缺失的spec_no:")
            for i, spec_no in enumerate(list(self.missing_spec_nos)[:10]):
                print(f"  {i+1}. {spec_no}")
            if missing_count > 10:
                print(f"  ... 还有 {missing_count - 10} 个")
        
        return coverage_rate

def main():
    """主函数"""
    print("🎯 100%货品编号匹配解决方案")
    print("=" * 60)
    
    collector = Complete100PercentGoodsCollector()
    
    # 加载现有档案
    collector.load_existing_archive()
    
    # 获取所有当前需要的spec_no
    target_spec_nos = collector.get_all_current_spec_nos()
    
    if not collector.missing_spec_nos:
        print("🎉 所有spec_no都已有档案！")
        return collector
    
    # 策略1: 扩展时间范围查询（1年）
    print(f"\n📅 策略1: 扩展时间范围查询（1年）")
    collector.extended_time_range_query(days_back=365)
    
    # 策略2: 针对缺失的spec_no进行定向查询
    print(f"\n🎯 策略2: 定向查询缺失的spec_no")
    collector.targeted_spec_no_query()
    
    # 策略3: 尝试其他策略
    print(f"\n🔧 策略3: 尝试其他策略")
    collector.try_alternative_strategies()
    
    # 分析最终结果
    coverage_rate = collector.analyze_final_results(target_spec_nos)
    
    # 保存结果
    archive_file = collector.save_complete_archive()
    
    print(f"\n🎉 100%货品编号匹配任务完成！")
    print(f"📁 档案文件: {archive_file}")
    print(f"📊 最终覆盖率: {coverage_rate:.1f}%")
    
    if coverage_rate >= 99.0:
        print("🏆 恭喜！达到99%以上的覆盖率！")
    elif coverage_rate >= 95.0:
        print("🎯 很好！达到95%以上的覆盖率！")
    else:
        print("💪 继续努力，可以考虑联系旺店通技术支持获取更多帮助")
    
    return collector

if __name__ == "__main__":
    main()
