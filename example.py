"""
旺店通WMS销售出库单查询示例
"""
import logging
from datetime import datetime, timedelta
import os

from wdt_client import WDTClient, WDTAPIException
from sales_stockout import SalesStockoutQuery
from data_processor import SalesStockoutProcessor


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('wdt_api.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )


def example_basic_query():
    """基础查询示例"""
    print("=== 基础查询示例 ===")
    
    try:
        # 创建查询器
        query = SalesStockoutQuery()
        
        # 查询最近1小时的销售出库单
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        print(f"查询时间范围: {start_time} - {end_time}")
        
        response = query.query_sales_stockout(
            start_time=start_time,
            end_time=end_time,
            status_type=0,  # 延时发货&已完成
            page_size=10
        )
        
        data = response.get('data', {})
        orders = data.get('order', [])
        total_count = data.get('total_count', 0)
        
        print(f"查询结果: 总共 {total_count} 条记录，当前页 {len(orders)} 条")
        
        for order in orders:
            print(f"出库单号: {order.get('order_no')}, "
                  f"订单号: {order.get('trade_no')}, "
                  f"状态: {order.get('status')}, "
                  f"发货时间: {order.get('consign_time')}")
        
        return orders
        
    except WDTAPIException as e:
        print(f"查询失败: {e}")
        return []


def example_date_range_query():
    """日期范围查询示例"""
    print("\n=== 日期范围查询示例 ===")
    
    try:
        # 创建查询器
        query = SalesStockoutQuery()
        
        # 查询昨天的销售出库单
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        
        print(f"查询日期: {yesterday}")
        
        orders = query.query_sales_stockout_by_date_range(
            start_date=yesterday,
            end_date=today,
            status_type=0  # 延时发货&已完成
        )
        
        print(f"查询结果: 总共 {len(orders)} 条记录")
        
        return orders
        
    except WDTAPIException as e:
        print(f"查询失败: {e}")
        return []


def example_specific_query():
    """特定条件查询示例"""
    print("\n=== 特定条件查询示例 ===")
    
    try:
        # 创建查询器
        query = SalesStockoutQuery()
        
        # 查询特定仓库的出库单
        end_time = datetime.now()
        start_time = end_time - timedelta(days=1)
        
        response = query.query_sales_stockout(
            start_time=start_time,
            end_time=end_time,
            status_type=3,  # 按指定状态查询
            status="110",   # 已完成
            # warehouse_no="",  # 请在此处填写实际仓库号
            need_sn=True,    # 返回SN信息
            get_anchor=True,  # 获取主播信息
            page_size=50
        )
        
        data = response.get('data', {})
        orders = data.get('order', [])
        
        print(f"特定条件查询结果: {len(orders)} 条记录")
        
        return orders
        
    except WDTAPIException as e:
        print(f"查询失败: {e}")
        return []


def example_data_export():
    """数据导出示例"""
    print("\n=== 数据导出示例 ===")
    
    try:
        # 获取数据
        query = SalesStockoutQuery()
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=2)
        
        orders = query.query_sales_stockout_all_pages(
            start_time=start_time,
            end_time=end_time,
            status_type=0,
            page_size=100
        )
        
        if not orders:
            print("没有查询到数据")
            return
        
        # 创建数据处理器
        processor = SalesStockoutProcessor()
        
        # 导出到Excel
        excel_file = processor.export_to_excel(orders)
        print(f"Excel文件已导出: {excel_file}")
        
        # 导出到CSV
        csv_file = processor.export_to_csv(orders)
        print(f"CSV文件已导出: {csv_file}")
        
        # 导出到JSON
        json_file = processor.export_to_json(orders)
        print(f"JSON文件已导出: {json_file}")
        
        # 获取统计信息
        stats = processor.get_summary_statistics(orders)
        print("\n统计信息:")
        for key, value in stats.items():
            if isinstance(value, dict):
                print(f"{key}:")
                for k, v in value.items():
                    print(f"  {k}: {v}")
            else:
                print(f"{key}: {value}")
        
    except WDTAPIException as e:
        print(f"数据导出失败: {e}")


def example_custom_client():
    """自定义客户端示例"""
    print("\n=== 自定义客户端示例 ===")
    
    try:
        # 使用自定义配置创建客户端
        client = WDTClient(
            sid="your_sid",
            app_key="your_app_key",
            app_secret="your_app_secret",
            api_url="https://openapi.wdtwms.com/open_api/service.php"
        )
        
        # 使用自定义客户端创建查询器
        query = SalesStockoutQuery(client)
        
        # 进行查询...
        print("自定义客户端创建成功")
        
    except Exception as e:
        print(f"自定义客户端创建失败: {e}")


def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    print("旺店通WMS销售出库单查询示例")
    print("=" * 50)
    
    # 检查配置
    try:
        from config import WDTConfig
        WDTConfig.validate_config()
        print("配置验证通过")
    except Exception as e:
        print(f"配置验证失败: {e}")
        print("请检查 .env 文件或环境变量配置")
        return
    
    # 运行示例
    try:
        # 基础查询
        orders1 = example_basic_query()
        
        # 日期范围查询
        orders2 = example_date_range_query()
        
        # 特定条件查询
        orders3 = example_specific_query()
        
        # 数据导出（使用第一个查询的结果）
        if orders1:
            example_data_export()
        
        # 自定义客户端
        example_custom_client()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")
        logging.exception("程序执行异常")


if __name__ == "__main__":
    main()
