#!/usr/bin/env python3
"""
旺店通旗舰版API客户端
基于官方文档：http://wdt.wangdian.cn/openapi
"""
import logging
import requests
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, List

from wdt_signature import WDTSignature
from config import WDTConfig


class WDTFlagshipAPIException(Exception):
    """旺店通旗舰版API异常"""
    
    def __init__(self, message: str, status: int = None):
        self.message = message
        self.status = status
        super().__init__(f"API Error {status}: {message}" if status else message)


class WDTFlagshipClient:
    """旺店通旗舰版API客户端"""
    
    def __init__(self, config: Optional[WDTConfig] = None):
        """
        初始化客户端
        
        Args:
            config: 配置类，如果为None则使用默认配置
        """
        self.config = config or WDTConfig
        self.base_url = "http://wdt.wangdian.cn/openapi"
        self.logger = logging.getLogger(__name__)
        
        # 创建session
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'WDT-Flagship-Client/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
    
    def _prepare_params(self, method: str, params: Dict[str, Any] = None, 
                       pager: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        准备请求参数（旗舰版格式）
        
        Args:
            method: 接口方法名
            params: 业务参数
            pager: 分页参数
            
        Returns:
            完整的请求参数
        """
        # 获取secret和salt
        secret, salt = self.config.get_secret_and_salt()
        
        # 构建基础参数
        request_params = {
            'sid': self.config.SID,
            'key': self.config.APP_KEY,
            'method': method,
            'v': '1.0',
            'timestamp': WDTSignature.generate_timestamp(),
        }
        
        # 添加salt参数（如果有）
        if salt:
            request_params['salt'] = salt
        
        # 添加业务参数
        if params:
            request_params['params'] = params
        
        # 添加分页参数
        if pager:
            request_params['pager'] = pager
        
        # 生成签名
        sign = WDTSignature.generate_sign(request_params, secret)
        request_params['sign'] = sign
        
        return request_params
    
    def _make_request(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            params: 请求参数
            
        Returns:
            响应数据
            
        Raises:
            WDTFlagshipAPIException: 请求失败
        """
        try:
            # 记录请求参数（隐藏敏感信息）
            log_params = params.copy()
            log_params['sign'] = '***'
            self.logger.debug(f"请求参数: {json.dumps(log_params, ensure_ascii=False, indent=2)}")
            
            # 发送POST请求 - 使用form-data格式
            # 需要将复杂参数转换为JSON字符串
            form_data = {}
            for key, value in params.items():
                if isinstance(value, (dict, list)):
                    form_data[key] = json.dumps(value, ensure_ascii=False)
                else:
                    form_data[key] = str(value)
            
            response = self.session.post(
                self.base_url,
                data=form_data,
                timeout=30
            )
            
            # 检查HTTP状态
            response.raise_for_status()
            
            # 解析JSON
            try:
                data = json.loads(response.content.decode('utf-8'))
            except json.JSONDecodeError as e:
                raise WDTFlagshipAPIException(f"响应JSON解析失败: {e}")
            
            self.logger.debug(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            # 检查业务状态
            status = data.get('status', -1)
            if status != 0:
                error_message = data.get('message', '未知错误')
                raise WDTFlagshipAPIException(error_message, status)
            
            return data
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"HTTP请求失败: {e}")
            raise WDTFlagshipAPIException(f"HTTP请求失败: {e}")
        except Exception as e:
            if isinstance(e, WDTFlagshipAPIException):
                raise
            self.logger.error(f"请求处理失败: {e}")
            raise WDTFlagshipAPIException(f"请求处理失败: {e}")
    
    def call_api(self, method: str, params: Dict[str, Any] = None,
                 page_size: int = 100, page_no: int = 0, calc_total: bool = True) -> Dict[str, Any]:
        """
        调用API接口
        
        Args:
            method: 接口方法名
            params: 业务参数
            page_size: 分页大小
            page_no: 页码（从0开始）
            calc_total: 是否计算总数
            
        Returns:
            API响应数据
            
        Raises:
            WDTFlagshipAPIException: API调用失败
        """
        # 构建分页参数
        pager = {
            'page_size': page_size,
            'page_no': page_no,
            'calc_total': 1 if calc_total else 0
        }
        
        # 准备完整参数
        request_params = self._prepare_params(method, params, pager)
        
        # 发送请求
        return self._make_request(request_params)
    
    def query_sales_trades(
        self,
        start_time: str,
        end_time: str,
        trade_status: int = 30,
        owner_no: Optional[str] = None,
        warehouse_no: Optional[str] = None,
        page_no: int = 0,
        page_size: int = 100
    ) -> Dict[str, Any]:
        """
        查询销售订单
        
        Args:
            start_time: 开始时间 (YYYY-MM-DD HH:MM:SS)
            end_time: 结束时间 (YYYY-MM-DD HH:MM:SS)
            trade_status: 订单状态
            owner_no: 货主编号（可选）
            warehouse_no: 仓库编号（可选）
            page_no: 页码（从0开始）
            page_size: 每页大小
            
        Returns:
            查询结果
            
        Raises:
            WDTFlagshipAPIException: API调用失败
        """
        # 构建业务参数
        business_params = {
            'start_time': start_time,
            'end_time': end_time,
            'trade_status': trade_status
        }
        
        # 添加可选参数
        if owner_no:
            business_params['owner_no'] = owner_no
        if warehouse_no:
            business_params['warehouse_no'] = warehouse_no
        
        # 调用API
        return self.call_api(
            method='sales.trade.query',
            params=business_params,
            page_size=page_size,
            page_no=page_no
        )
    
    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            连接是否成功
        """
        try:
            # 使用一个已知存在的接口测试连接
            end_time = datetime.now()
            start_time = end_time.replace(hour=end_time.hour-1)
            
            response = self.call_api(
                method='finance.settle.Logistics.search',
                params={
                    'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'status': 5
                },
                page_size=1,
                page_no=0
            )
            
            self.logger.info("API连接测试成功")
            return True
            
        except Exception as e:
            self.logger.error(f"API连接测试失败: {e}")
            return False
