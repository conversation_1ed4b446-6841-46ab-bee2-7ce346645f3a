#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导出当日销售出库明细单（包含货品编号和货品备注）
结合 goods.spec.query.step 接口获取货品档案信息
"""

import json
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient

class GoodsArchiveHelper:
    """货品档案助手类"""

    def __init__(self, client):
        self.client = client
        self.owner_no = "AJT-XLSWDT"  # 正确的货主编号
        self.goods_cache = {}  # 缓存查询到的货品信息

    def try_get_goods_info(self, spec_no):
        """尝试获取货品档案信息"""
        if spec_no in self.goods_cache:
            return self.goods_cache[spec_no]

        # 尝试通过 goods.spec.query.step 查询
        # 由于是增量接口，尝试多个时间范围
        now = datetime.now()
        time_ranges = [
            # 最近几天的时间范围
            (now - timedelta(days=1), now - timedelta(days=1) + timedelta(hours=1)),
            (now - timedelta(days=2), now - timedelta(days=2) + timedelta(hours=1)),
            (now - timedelta(days=3), now - timedelta(days=3) + timedelta(hours=1)),
        ]

        for start_time, end_time in time_ranges:
            try:
                response = self.client.call_api('goods.spec.query.step', {
                    "owner_no": self.owner_no,
                    "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "page_size": 100,
                    "page_no": 0
                })

                if response and 'content' in response:
                    content = response.get('content', [])
                    if isinstance(content, list):
                        for item in content:
                            item_spec_no = item.get('spec_no', '')
                            if item_spec_no:
                                # 缓存所有找到的商品信息
                                self.goods_cache[item_spec_no] = {
                                    'goods_no': item.get('goods_no', ''),
                                    'remark': item.get('remark', ''),
                                    'description': item.get('description', ''),
                                    'memo': item.get('memo', ''),
                                    'note': item.get('note', ''),
                                    'full_data': item
                                }

                                if item_spec_no == spec_no:
                                    return self.goods_cache[spec_no]
            except:
                continue

        # 如果没有找到，返回空信息
        empty_info = {
            'goods_no': '',
            'remark': '',
            'description': '',
            'memo': '',
            'note': '',
            'full_data': None
        }
        self.goods_cache[spec_no] = empty_info
        return empty_info

def export_with_goods_remark():
    """导出当日销售出库明细单（包含货品编号和货品备注）"""

    print("📦 导出当日销售出库明细单（包含货品编号和货品备注）")
    print("=" * 60)
    
    # 创建客户端和货品档案助手
    client = WDTPostClient()
    goods_helper = GoodsArchiveHelper(client)

    print("🔍 初始化货品档案查询助手...")
    print(f"📦 货主编号: {goods_helper.owner_no}")
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 时间范围: {start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")
    
    # 获取所有已发货订单
    print(f"\n📊 正在获取所有已发货订单...")
    
    all_stockouts = []
    unique_stockouts = {}
    page_size = 30
    max_pages = 20
    
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": page_size
    }
    
    page_no = 0
    consecutive_empty_pages = 0
    
    while page_no < max_pages and consecutive_empty_pages < 5:
        print(f"\n📄 正在获取第 {page_no + 1} 页数据...")
        
        try:
            current_params = {**base_params, "page_no": page_no}
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                print(f"❌ 第 {page_no + 1} 页响应为空")
                consecutive_empty_pages += 1
                page_no += 1
                continue
            
            content = response.get('content', [])
            total = response.get('total', 0)
            
            print(f"📊 第 {page_no + 1} 页: API返回 total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
            
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
                if not isinstance(page_data, list):
                    page_data = [page_data] if page_data else []
            
            if not page_data:
                print(f"📝 第 {page_no + 1} 页无数据")
                consecutive_empty_pages += 1
                page_no += 1
                continue
            
            consecutive_empty_pages = 0
            
            # 处理数据并去重
            shipped_count = 0
            for item in page_data:
                if isinstance(item, dict):
                    stockout_no = item.get('stockout_no', '')
                    if stockout_no and stockout_no not in unique_stockouts:
                        status = str(item.get('status', ''))
                        if status == '95':  # 已发货状态
                            unique_stockouts[stockout_no] = item
                            shipped_count += 1
            
            if shipped_count > 0:
                print(f"✅ 第 {page_no + 1} 页: {shipped_count} 条已发货订单")
                print(f"📈 累计已发货订单: {len(unique_stockouts)} 条")
            
            page_no += 1
                
        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            page_no += 1
            continue
    
    all_stockouts = list(unique_stockouts.values())
    print(f"\n📊 查询完成！总计获取 {len(all_stockouts)} 条已发货订单")
    
    if not all_stockouts:
        print("📝 今天没有已发货的订单")
        return
    
    # 导出到Excel
    filename = f"销售出库明细单_含货品备注_{today.strftime('%Y%m%d')}.xlsx"
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 设置边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 创建主表（出库单汇总）
        ws_main = wb.active
        ws_main.title = "出库单汇总"
        
        # 主表字段
        main_headers = [
            '出库单号', '原始订单号', '交易号', '店铺名称', '仓库名称',
            '物流公司', '物流单号', '买家昵称', '收件人姓名', '收件人电话',
            '收件人地址', '订单金额', '商品数量', '发货时间', '状态'
        ]
        
        # 写入主表表头
        for col, header in enumerate(main_headers, 1):
            cell = ws_main.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = thin_border
        
        # 写入主表数据
        for row_idx, stockout in enumerate(all_stockouts, 2):
            row_data = [
                stockout.get('stockout_no', ''),
                stockout.get('src_order_no', ''),
                stockout.get('trade_no', ''),
                stockout.get('shop_name', ''),
                stockout.get('warehouse_name', ''),
                stockout.get('logistics_name', ''),
                stockout.get('logistics_no', ''),
                stockout.get('buyer_nick', ''),
                stockout.get('receiver_name', ''),
                stockout.get('receiver_mobile', ''),
                # 拼接完整地址
                ' '.join([
                    stockout.get('receiver_province', ''),
                    stockout.get('receiver_city', ''),
                    stockout.get('receiver_district', ''),
                    stockout.get('receiver_address', '')
                ]).strip(),
                stockout.get('total_amount', ''),
                stockout.get('goods_count', ''),
                stockout.get('consign_time', ''),
                '已发货'
            ]
            
            for col_idx, value in enumerate(row_data, 1):
                cell = ws_main.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border
                cell.alignment = Alignment(vertical="center")
        
        # 自动调整主表列宽
        for column in ws_main.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_main.column_dimensions[column_letter].width = adjusted_width
        
        # 创建商品明细表
        ws_detail = wb.create_sheet("商品明细")
        
        # 商品明细表字段（包含货品编号和货品备注）
        detail_headers = [
            '出库单号', '原始订单号', '货品编号(goods_no)', '规格编号(spec_no)', '商品名称', '规格名称',
            '商品数量', '单价', '总金额', '重量', '条码', '出库备注', '货品档案备注'
        ]
        
        # 写入明细表表头
        for col, header in enumerate(detail_headers, 1):
            cell = ws_detail.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = thin_border
        
        # 写入商品明细数据
        detail_row = 2
        total_detail_count = 0
        stockout_remark_count = 0  # 统计有出库备注的商品数量
        archive_remark_count = 0   # 统计有货品档案备注的商品数量
        goods_no_count = 0         # 统计有货品编号的商品数量

        print(f"\n🔍 开始查询货品档案信息...")

        for stockout in all_stockouts:
            stockout_no = stockout.get('stockout_no', '')
            src_order_no = stockout.get('src_order_no', '')

            # 获取商品明细
            goods_detail = stockout.get('goods_detail', [])
            if isinstance(goods_detail, list) and goods_detail:
                for detail in goods_detail:
                    spec_no = detail.get('spec_no', '')

                    # 获取出库单中的备注
                    stockout_remark = detail.get('remark', '')
                    if stockout_remark:
                        stockout_remark_count += 1

                    # 尝试获取货品档案信息
                    print(f"🔍 查询货品档案: {spec_no}")
                    goods_info = goods_helper.try_get_goods_info(spec_no)

                    goods_no = goods_info.get('goods_no', '')
                    archive_remark = (goods_info.get('remark') or
                                    goods_info.get('description') or
                                    goods_info.get('memo') or
                                    goods_info.get('note') or '')

                    if goods_no:
                        goods_no_count += 1
                        print(f"  ✅ 找到货品编号: {goods_no}")

                    if archive_remark:
                        archive_remark_count += 1
                        print(f"  ✅ 找到货品档案备注: {archive_remark}")

                    detail_data = [
                        stockout_no,
                        src_order_no,
                        goods_no,                            # 货品编号
                        spec_no,                             # 规格编号
                        detail.get('goods_name', ''),        # 商品名称
                        detail.get('spec_name', ''),         # 规格名称
                        detail.get('num', ''),               # 商品数量
                        detail.get('order_price', ''),       # 单价
                        detail.get('goods_total_amount', ''), # 总金额
                        detail.get('gross_weight', ''),      # 重量
                        detail.get('barcode', ''),           # 条码
                        stockout_remark,                     # 出库备注
                        archive_remark                       # 货品档案备注
                    ]

                    for col_idx, value in enumerate(detail_data, 1):
                        cell = ws_detail.cell(row=detail_row, column=col_idx, value=value)
                        cell.border = thin_border
                        cell.alignment = Alignment(vertical="center")

                        # 高亮显示有数据的重要字段
                        if col_idx == 3 and goods_no:  # 货品编号
                            cell.fill = PatternFill(start_color="E8F5E8", end_color="E8F5E8", fill_type="solid")
                        elif col_idx == 12 and stockout_remark:  # 出库备注
                            cell.fill = PatternFill(start_color="FFFACD", end_color="FFFACD", fill_type="solid")
                        elif col_idx == 13 and archive_remark:  # 货品档案备注
                            cell.fill = PatternFill(start_color="E8F4FD", end_color="E8F4FD", fill_type="solid")

                    detail_row += 1
                    total_detail_count += 1
            else:
                # 如果没有明细，至少记录一行基本信息
                no_detail_data = [stockout_no, src_order_no, '无明细数据', '', '', '', '', '', '', '', '']
                for col_idx, value in enumerate(no_detail_data, 1):
                    cell = ws_detail.cell(row=detail_row, column=col_idx, value=value)
                    cell.border = thin_border
                    cell.alignment = Alignment(vertical="center")
                detail_row += 1
        
        # 自动调整明细表列宽
        for column in ws_detail.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_detail.column_dimensions[column_letter].width = adjusted_width
        
        # 创建统计汇总表
        ws_summary = wb.create_sheet("统计汇总")
        
        # 统计数据
        total_amount = sum(float(s.get('total_amount', 0) or 0) for s in all_stockouts)
        total_goods = sum(float(s.get('goods_count', 0) or 0) for s in all_stockouts)
        
        # 写入统计数据
        summary_data = [
            ['统计项目', '数值', '说明'],
            ['查询日期', today.strftime('%Y-%m-%d'), '数据查询的日期'],
            ['导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '文件生成时间'],
            ['已发货订单总数', len(all_stockouts), '状态为95的出库单数量'],
            ['商品明细总数', total_detail_count, '所有商品明细条数'],
            ['有货品编号的商品数', goods_no_count, '成功获取到货品编号的商品数量'],
            ['有出库备注的商品数', stockout_remark_count, '出库单中包含备注的商品数量'],
            ['有货品档案备注的商品数', archive_remark_count, '货品档案中包含备注的商品数量'],
            ['货品编号覆盖率', f'{goods_no_count/total_detail_count*100:.1f}%' if total_detail_count > 0 else '0%', '有货品编号商品占总商品的比例'],
            ['档案备注覆盖率', f'{archive_remark_count/total_detail_count*100:.1f}%' if total_detail_count > 0 else '0%', '有档案备注商品占总商品的比例'],
            ['订单总金额', f'{total_amount:.2f}', '所有订单金额汇总'],
            ['商品总数量', f'{total_goods:.0f}', '所有商品数量汇总']
        ]
        
        for row_idx, row_data in enumerate(summary_data, 1):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws_summary.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border
                if row_idx == 1:  # 表头
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                else:
                    cell.alignment = Alignment(vertical="center")
        
        # 调整统计表列宽
        ws_summary.column_dimensions['A'].width = 20
        ws_summary.column_dimensions['B'].width = 20
        ws_summary.column_dimensions['C'].width = 30
        
        # 保存文件
        wb.save(filename)
        
        print(f"✅ 导出成功！")
        print(f"📊 已发货订单: {len(all_stockouts)} 条")
        print(f"📋 商品明细: {total_detail_count} 条")
        print(f"� 有货品编号的商品: {goods_no_count} 条")
        print(f"�📝 有出库备注的商品: {stockout_remark_count} 条")
        print(f"� 有货品档案备注的商品: {archive_remark_count} 条")
        print(f"�📊 货品编号覆盖率: {goods_no_count/total_detail_count*100:.1f}%" if total_detail_count > 0 else "0%")
        print(f"📊 档案备注覆盖率: {archive_remark_count/total_detail_count*100:.1f}%" if total_detail_count > 0 else "0%")
        print(f"💰 订单总金额: {total_amount:.2f}")
        print(f"📦 商品总数量: {total_goods:.0f}")
        print(f"📁 文件路径: {filename}")
        print(f"📋 包含三个工作表:")
        print(f"   - 出库单汇总: 订单基本信息")
        print(f"   - 商品明细: 详细商品信息（含货品编号和货品档案备注）")
        print(f"   - 统计汇总: 数据统计信息")

        print(f"\n🎉 销售出库明细单导出完成！")
        print(f"\n📝 货品信息说明:")
        print(f"   ✅ 货品编号字段: goods_no（通过goods.spec.query.step接口获取）")
        print(f"   ✅ 货品档案备注: 通过货品档案接口获取的完整备注信息")
        print(f"   📊 货品编号统计: {goods_no_count}/{total_detail_count} 条商品有货品编号")
        print(f"   📊 档案备注统计: {archive_remark_count}/{total_detail_count} 条商品有档案备注")
        print(f"   🎨 有数据的字段在Excel中会用不同颜色高亮显示")
        
    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    export_with_goods_remark()
