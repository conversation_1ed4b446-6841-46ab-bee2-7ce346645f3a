#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取所有货品档案中的备注信息
专门针对货品备注的收集和分析
"""

import json
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient

class GoodsRemarksCollector:
    """货品备注收集器"""
    
    def __init__(self):
        self.client = WDTPostClient()
        self.owner_no = "AJT-XLSWDT"
        self.goods_with_remarks = {}
        self.all_goods_data = {}
        
    def comprehensive_remarks_search(self, days_back=180):
        """全面搜索货品备注"""
        
        print(f"🔍 全面搜索最近 {days_back} 天的货品备注信息...")
        
        now = datetime.now()
        total_goods = 0
        goods_with_remarks = 0
        
        # 按天查询，每天查询多个时间段
        for day in range(days_back):
            query_date = now - timedelta(days=day)
            
            # 每天查询多个时间段，增加找到备注的概率
            time_slots = [
                (0, 1), (3, 4), (6, 7), (9, 10), 
                (12, 13), (15, 16), (18, 19), (21, 22)
            ]
            
            day_goods = 0
            day_remarks = 0
            
            for start_hour, end_hour in time_slots:
                start_time = query_date.replace(hour=start_hour, minute=0, second=0, microsecond=0)
                end_time = query_date.replace(hour=end_hour, minute=0, second=0, microsecond=0)
                
                try:
                    # 尝试多页查询
                    for page_no in range(5):  # 查询前5页
                        response = self.client.call_api('goods.spec.query.step', {
                            "owner_no": self.owner_no,
                            "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "page_size": 100,
                            "page_no": page_no
                        })
                        
                        if response and 'content' in response:
                            content = response.get('content', [])
                            if isinstance(content, list) and content:
                                for item in content:
                                    spec_no = item.get('spec_no', '')
                                    if spec_no and spec_no not in self.all_goods_data:
                                        # 保存所有商品数据
                                        self.all_goods_data[spec_no] = item
                                        total_goods += 1
                                        day_goods += 1
                                        
                                        # 检查各种可能的备注字段
                                        remark_fields = [
                                            'remark', 'description', 'memo', 'note', 
                                            'goods_remark', 'spec_remark', 'product_remark',
                                            'comment', 'remarks', 'desc'
                                        ]
                                        
                                        found_remark = ''
                                        remark_field = ''
                                        
                                        for field in remark_fields:
                                            value = item.get(field, '')
                                            if value and str(value).strip():
                                                found_remark = str(value).strip()
                                                remark_field = field
                                                break
                                        
                                        if found_remark:
                                            self.goods_with_remarks[spec_no] = {
                                                'spec_no': spec_no,
                                                'goods_no': item.get('goods_no', ''),
                                                'goods_name': item.get('goods_name', ''),
                                                'spec_name': item.get('spec_name', ''),
                                                'remark': found_remark,
                                                'remark_field': remark_field,
                                                'barcode': item.get('barcode', ''),
                                                'brand_name': item.get('brand_name', ''),
                                                'update_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                                                'full_data': item
                                            }
                                            goods_with_remarks += 1
                                            day_remarks += 1
                                            print(f"  📝 找到备注: {spec_no} - {found_remark[:50]}...")
                            else:
                                break  # 没有更多数据，跳出分页循环
                        else:
                            break  # API调用失败，跳出分页循环
                
                except Exception as e:
                    continue
            
            if day_goods > 0:
                print(f"📅 {query_date.strftime('%Y-%m-%d')}: 找到 {day_goods} 个商品，其中 {day_remarks} 个有备注")
            
            # 每查询10天显示一次进度
            if (day + 1) % 10 == 0:
                print(f"📊 已查询 {day + 1} 天，累计找到 {total_goods} 个商品，{goods_with_remarks} 个有备注")
        
        print(f"\n📊 全面搜索完成！")
        print(f"📦 总计找到 {total_goods} 个商品")
        print(f"📝 其中有备注的商品: {goods_with_remarks} 个")
        print(f"📈 备注覆盖率: {goods_with_remarks/total_goods*100:.1f}%" if total_goods > 0 else "0%")
        
        return total_goods, goods_with_remarks
    
    def analyze_remarks(self):
        """分析备注信息"""
        if not self.goods_with_remarks:
            print("❌ 没有找到任何备注信息")
            return
        
        print(f"\n📊 备注信息分析:")
        print(f"📝 有备注的商品总数: {len(self.goods_with_remarks)}")
        
        # 按备注字段类型统计
        field_stats = {}
        for item in self.goods_with_remarks.values():
            field = item.get('remark_field', 'unknown')
            field_stats[field] = field_stats.get(field, 0) + 1
        
        print(f"\n📋 备注字段类型统计:")
        for field, count in sorted(field_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"  {field}: {count} 个商品")
        
        # 备注长度统计
        remark_lengths = [len(item.get('remark', '')) for item in self.goods_with_remarks.values()]
        if remark_lengths:
            avg_length = sum(remark_lengths) / len(remark_lengths)
            max_length = max(remark_lengths)
            min_length = min(remark_lengths)
            
            print(f"\n📏 备注长度统计:")
            print(f"  平均长度: {avg_length:.1f} 字符")
            print(f"  最长备注: {max_length} 字符")
            print(f"  最短备注: {min_length} 字符")
        
        # 显示一些备注示例
        print(f"\n📝 备注内容示例:")
        count = 0
        for spec_no, item in self.goods_with_remarks.items():
            if count >= 10:
                break
            remark = item.get('remark', '')
            goods_name = item.get('goods_name', '')
            print(f"  {count+1}. {spec_no} ({goods_name})")
            print(f"     备注: {remark}")
            count += 1
    
    def save_remarks_data(self):
        """保存备注数据"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存有备注的商品
        remarks_filename = f"goods_with_remarks_{timestamp}.json"
        with open(remarks_filename, 'w', encoding='utf-8') as f:
            json.dump(self.goods_with_remarks, f, ensure_ascii=False, indent=2)
        
        # 保存所有商品数据
        all_goods_filename = f"all_goods_data_{timestamp}.json"
        with open(all_goods_filename, 'w', encoding='utf-8') as f:
            json.dump(self.all_goods_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 数据已保存:")
        print(f"  有备注的商品: {remarks_filename}")
        print(f"  所有商品数据: {all_goods_filename}")
        
        return remarks_filename, all_goods_filename
    
    def export_remarks_to_excel(self):
        """导出备注信息到Excel"""
        if not self.goods_with_remarks:
            print("❌ 没有备注数据可导出")
            return
        
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            
            filename = f"货品备注信息_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            
            print(f"\n📊 导出备注信息到Excel...")
            print(f"📁 文件名: {filename}")
            
            # 创建工作簿
            wb = Workbook()
            
            # 设置边框样式
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # 创建备注信息表
            ws = wb.active
            ws.title = "货品备注信息"
            
            # 表头
            headers = [
                '货品编号(goods_no)', '规格编号(spec_no)', '商品名称', '规格名称',
                '货品备注', '备注字段', '条码', '品牌', '更新时间'
            ]
            
            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
                cell.alignment = Alignment(horizontal="center", vertical="center")
                cell.border = thin_border
            
            # 写入数据
            for row_idx, (spec_no, item) in enumerate(self.goods_with_remarks.items(), 2):
                row_data = [
                    item.get('goods_no', ''),
                    spec_no,
                    item.get('goods_name', ''),
                    item.get('spec_name', ''),
                    item.get('remark', ''),
                    item.get('remark_field', ''),
                    item.get('barcode', ''),
                    item.get('brand_name', ''),
                    item.get('update_time', '')
                ]
                
                for col_idx, value in enumerate(row_data, 1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    cell.border = thin_border
                    cell.alignment = Alignment(vertical="center")
                    
                    # 高亮备注列
                    if col_idx == 5:  # 备注列
                        cell.fill = PatternFill(start_color="FFFACD", end_color="FFFACD", fill_type="solid")
            
            # 自动调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 80)  # 备注列可能很长
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 创建统计表
            ws_stats = wb.create_sheet("统计信息")
            
            # 统计数据
            total_goods = len(self.all_goods_data)
            goods_with_remarks = len(self.goods_with_remarks)
            coverage_rate = goods_with_remarks / total_goods * 100 if total_goods > 0 else 0
            
            stats_data = [
                ['统计项目', '数值', '说明'],
                ['查询时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '数据查询的时间'],
                ['总商品数', total_goods, '查询到的所有商品数量'],
                ['有备注的商品数', goods_with_remarks, '包含备注信息的商品数量'],
                ['备注覆盖率', f'{coverage_rate:.1f}%', '有备注商品占总商品的比例'],
                ['数据来源', 'goods.spec.query.step', '旺店通WMS货品档案接口'],
                ['查询范围', '最近180天', '查询的时间范围']
            ]
            
            for row_idx, row_data in enumerate(stats_data, 1):
                for col_idx, value in enumerate(row_data, 1):
                    cell = ws_stats.cell(row=row_idx, column=col_idx, value=value)
                    cell.border = thin_border
                    if row_idx == 1:  # 表头
                        cell.font = Font(bold=True, color="FFFFFF")
                        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                        cell.alignment = Alignment(horizontal="center", vertical="center")
                    else:
                        cell.alignment = Alignment(vertical="center")
            
            # 调整统计表列宽
            ws_stats.column_dimensions['A'].width = 20
            ws_stats.column_dimensions['B'].width = 20
            ws_stats.column_dimensions['C'].width = 40
            
            # 保存文件
            wb.save(filename)
            
            print(f"✅ Excel导出成功！")
            print(f"📊 有备注的商品: {goods_with_remarks} 个")
            print(f"📈 备注覆盖率: {coverage_rate:.1f}%")
            print(f"📁 文件路径: {filename}")
            
            return filename
            
        except ImportError:
            print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
            return None
        except Exception as e:
            print(f"❌ Excel导出失败: {e}")
            return None

def main():
    """主函数"""
    print("📝 获取所有货品档案中的备注信息")
    print("=" * 60)
    
    collector = GoodsRemarksCollector()
    
    # 全面搜索备注信息
    total_goods, goods_with_remarks = collector.comprehensive_remarks_search(days_back=180)
    
    if goods_with_remarks == 0:
        print("❌ 没有找到任何备注信息")
        print("💡 建议:")
        print("1. 扩大查询时间范围")
        print("2. 检查货品档案中是否确实有备注")
        print("3. 联系旺店通技术支持确认备注字段名称")
        return
    
    # 分析备注信息
    collector.analyze_remarks()
    
    # 保存数据
    remarks_file, all_goods_file = collector.save_remarks_data()
    
    # 导出到Excel
    excel_file = collector.export_remarks_to_excel()
    
    print(f"\n🎉 货品备注信息收集完成！")
    print(f"📊 总计找到 {total_goods} 个商品")
    print(f"📝 其中有备注的商品: {goods_with_remarks} 个")
    print(f"📈 备注覆盖率: {goods_with_remarks/total_goods*100:.1f}%")
    
    if excel_file:
        print(f"📁 Excel文件: {excel_file}")
    
    return collector

if __name__ == "__main__":
    main()
