#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
尝试 goods.spec 相关的其他接口变体
"""

import json
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient

def try_goods_spec_variations():
    """尝试 goods.spec 相关的其他接口变体"""
    
    print("🔍 尝试 goods.spec 相关的其他接口变体")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 正确的货主编号
    owner_no = "AJT-XLSWDT"
    print(f"📦 使用货主编号: {owner_no}")
    
    # 先获取一些spec_no用于测试
    print("📦 先获取一些商品的spec_no...")
    
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    spec_nos = []
    try:
        response = client.call_api('stockout.query', {
            "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 5,
            "page_no": 0
        })
        
        if response and 'content' in response:
            content = response.get('content', [])
            if isinstance(content, list):
                for stockout in content:
                    goods_detail = stockout.get('goods_detail', [])
                    if isinstance(goods_detail, list):
                        for detail in goods_detail:
                            spec_no = detail.get('spec_no')
                            if spec_no and spec_no not in spec_nos:
                                spec_nos.append(spec_no)
                                if len(spec_nos) >= 3:
                                    break
                    if len(spec_nos) >= 3:
                        break
        
        print(f"✅ 获取到测试用的spec_no: {spec_nos}")
    except Exception as e:
        print(f"❌ 获取spec_no失败: {e}")
        spec_nos = ["1683214231569040384", "1986391445790819712"]
        print(f"🔄 使用固定的spec_no进行测试: {spec_nos}")
    
    # 尝试不同的goods.spec相关接口
    goods_spec_apis = [
        'goods.spec.query',
        'goods.spec.get',
        'goods.spec.list',
        'goods.spec.info',
        'goods.spec.detail',
        'goods.spec.search',
        'goods.spec.find',
        'spec.query',
        'spec.get',
        'spec.list',
        'spec.info',
        'spec.detail'
    ]
    
    successful_apis = []
    
    for api_name in goods_spec_apis:
        print(f"\n📋 测试API: {api_name}")
        
        # 尝试不同的参数组合
        test_params = [
            # 基础查询
            {"owner_no": owner_no, "page_size": 10, "page_no": 0},
            
            # 按spec_no查询
            {"owner_no": owner_no, "spec_no": spec_nos[0]} if spec_nos else {},
            
            # 批量查询
            {"owner_no": owner_no, "spec_nos": ",".join(spec_nos[:2])} if len(spec_nos) >= 2 else {},
            
            # 带状态参数
            {"owner_no": owner_no, "page_size": 10, "page_no": 0, "status": 1},
            {"owner_no": owner_no, "page_size": 10, "page_no": 0, "is_enabled": 1},
            
            # 不带货主编号（可能不需要）
            {"page_size": 10, "page_no": 0},
            {"spec_no": spec_nos[0]} if spec_nos else {},
        ]
        
        for i, params in enumerate(test_params, 1):
            if not params:
                continue
                
            print(f"  🔍 尝试参数组合 {i}: {params}")
            
            try:
                response = client.call_api(api_name, params)
                
                if response and 'content' in response:
                    content = response.get('content', [])
                    total = response.get('total', 0)
                    
                    print(f"    ✅ API调用成功: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
                    
                    if isinstance(content, list) and content:
                        first_item = content[0]
                        print(f"    📋 第一条数据的关键字段:")
                        
                        # 查找关键字段
                        key_fields = {}
                        for key, value in first_item.items():
                            if any(keyword in key.lower() for keyword in ['goods_no', 'spec_no', 'remark', 'note', 'name']):
                                key_fields[key] = value
                                print(f"      🔍 {key}: {value}")
                        
                        if key_fields:
                            successful_apis.append({
                                'api_name': api_name,
                                'params': params,
                                'total': total,
                                'key_fields': key_fields,
                                'sample_data': first_item
                            })
                            print(f"    ✅ 找到有用的数据！")
                            
                            # 如果找到了有用的数据，保存并继续测试其他接口
                            break
                    
                    elif total == 0:
                        print(f"    📝 接口可用但无数据")
                    
                    else:
                        print(f"    ❌ 返回数据格式异常")
                
                else:
                    print(f"    ❌ API返回数据为空或格式错误")
            
            except Exception as e:
                error_msg = str(e)
                if "无该接口" in error_msg or "调用权限" in error_msg:
                    print(f"    ❌ 无权限: {error_msg}")
                    break  # 如果没有权限，不用尝试其他参数
                elif "未部署" in error_msg or "暂不支持" in error_msg:
                    print(f"    ❌ 不支持: {error_msg}")
                    break
                else:
                    print(f"    ❌ 调用失败: {e}")
    
    # 总结结果
    print(f"\n📝 测试结果总结:")
    if successful_apis:
        print(f"✅ 找到 {len(successful_apis)} 个可用的API:")
        for api_info in successful_apis:
            print(f"  📋 {api_info['api_name']}:")
            print(f"    参数: {api_info['params']}")
            print(f"    总数: {api_info['total']}")
            print(f"    关键字段: {list(api_info['key_fields'].keys())}")
        
        # 保存成功的API信息
        success_filename = f"successful_goods_spec_apis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(success_filename, 'w', encoding='utf-8') as f:
            json.dump(successful_apis, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 成功的API信息已保存到: {success_filename}")
        return successful_apis
    
    else:
        print(f"❌ 没有找到可用的货品档案API")
        print(f"\n💡 可能的原因:")
        print(f"1. goods.spec.query.step 是增量查询接口，只返回变更的数据")
        print(f"2. 需要其他类型的接口来查询完整的货品档案")
        print(f"3. 可能需要联系旺店通确认正确的接口名称")
        
        return []

if __name__ == "__main__":
    try_goods_spec_variations()
