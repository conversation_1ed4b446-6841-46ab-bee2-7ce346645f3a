#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
强制获取所有476条记录 - 通过大量分页调用
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def force_get_all_476():
    """强制获取所有476条记录"""
    
    print("🎯 强制获取所有476条已发货记录")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 时间范围: {start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")
    
    # 发现：API每次只返回30条，不管页面大小设置多少
    # 解决方案：强制分页查询，直到获取所有数据
    
    print(f"\n🔍 发现：API每次固定返回30条记录")
    print(f"📊 目标：获取所有476条记录")
    print(f"📑 需要查询页数：{476 // 30 + 1} 页")
    
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 100  # 虽然无效，但保持设置
    }
    
    all_stockouts = []
    all_records = []  # 存储所有记录（包括非已发货）
    page_no = 0
    consecutive_empty = 0
    
    print(f"\n🚀 开始强制分页查询...")
    
    while len(all_records) < 476:  # 继续查询直到获取所有记录
        print(f"\n📄 正在获取第 {page_no + 1} 页数据...")
        
        try:
            current_params = base_params.copy()
            current_params['page_no'] = page_no
            
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                print(f"❌ 第 {page_no + 1} 页响应为空")
                consecutive_empty += 1
                if consecutive_empty >= 3:
                    print("🛑 连续3页空响应，停止查询")
                    break
                page_no += 1
                continue
            
            content = response.get('content', [])
            total = response.get('total', 0)
            
            print(f"📊 第 {page_no + 1} 页: API total={total}, content数量={len(content)}")
            
            # 处理数据
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
                if not isinstance(page_data, list):
                    page_data = [page_data] if page_data else []
            
            if not page_data:
                print(f"📝 第 {page_no + 1} 页无数据")
                consecutive_empty += 1
                if consecutive_empty >= 3:
                    print("🛑 连续3页无数据，停止查询")
                    break
                page_no += 1
                continue
            
            # 重置连续空页计数
            consecutive_empty = 0
            
            # 添加所有记录
            all_records.extend(page_data)
            
            # 筛选已发货数据
            shipped_data = []
            for item in page_data:
                if isinstance(item, dict):
                    status = str(item.get('status', ''))
                    if status == '95':  # 已发货状态
                        shipped_data.append(item)
            
            print(f"✅ 第 {page_no + 1} 页: 总数据 {len(page_data)} 条, 已发货 {len(shipped_data)} 条")
            
            if shipped_data:
                all_stockouts.extend(shipped_data)
            
            print(f"📈 累计: 总记录 {len(all_records)} 条, 已发货 {len(all_stockouts)} 条")
            
            # 进度显示
            progress = (len(all_records) / 476) * 100
            print(f"📊 进度: {progress:.1f}% ({len(all_records)}/476)")
            
            page_no += 1
            
            # 安全限制：最多查询100页
            if page_no >= 100:
                print(f"⚠️ 已查询100页，停止查询")
                break
                
        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            consecutive_empty += 1
            if consecutive_empty >= 5:
                print("🛑 连续5页失败，停止查询")
                break
            page_no += 1
            continue
    
    print(f"\n📊 查询完成！")
    print(f"📈 总记录: {len(all_records)} 条")
    print(f"🚚 已发货: {len(all_stockouts)} 条")
    print(f"📊 已发货比例: {len(all_stockouts)/len(all_records)*100:.1f}%" if all_records else "0%")
    
    # 分析未获取到所有记录的原因
    if len(all_records) < 476:
        print(f"\n⚠️ 分析：只获取了 {len(all_records)}/476 条记录")
        print(f"可能原因：")
        print(f"   1. API分页机制限制")
        print(f"   2. 时间范围内实际记录数少于476")
        print(f"   3. 权限或状态筛选限制")
        print(f"   4. API内部缓存或索引问题")
    
    # 导出结果
    if all_stockouts:
        filename = f"强制获取已发货订单_{today.strftime('%Y%m%d')}.xlsx"
        
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Alignment
            
            print(f"\n📊 开始导出到Excel...")
            print(f"📁 文件名: {filename}")
            
            wb = Workbook()
            
            # 创建已发货订单表
            ws_shipped = wb.active
            ws_shipped.title = "已发货订单"
            
            # 主要字段
            main_headers = [
                'stockout_no', 'src_order_no', 'trade_no', 'src_tids',
                'warehouse_name', 'shop_name', 'platform_name',
                'logistics_name', 'logistics_no', 'consign_time',
                'total_amount', 'goods_count', 'buyer_nick',
                'receiver_province', 'receiver_city', 'receiver_district',
                'status', 'trade_time', 'pay_time'
            ]
            
            # 写入表头
            for col, header in enumerate(main_headers, 1):
                cell = ws_shipped.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")
            
            # 写入已发货数据
            for row_idx, stockout in enumerate(all_stockouts, 2):
                for col_idx, header in enumerate(main_headers, 1):
                    value = stockout.get(header, '')
                    if isinstance(value, (dict, list)):
                        value = json.dumps(value, ensure_ascii=False)
                    elif value is None:
                        value = ''
                    ws_shipped.cell(row=row_idx, column=col_idx, value=str(value))
            
            # 创建统计表
            ws_stats = wb.create_sheet("数据统计")
            
            stats_headers = ['统计项目', '数值', '说明']
            for col, header in enumerate(stats_headers, 1):
                cell = ws_stats.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            
            # 统计数据
            total_amount = 0
            total_goods = 0
            logistics_stats = {}
            warehouse_stats = {}
            
            for stockout in all_stockouts:
                # 金额统计
                amount = stockout.get('total_amount', 0)
                if amount:
                    try:
                        total_amount += float(amount)
                    except:
                        pass
                
                # 商品数量统计
                goods_count = stockout.get('goods_count', 0)
                if goods_count:
                    try:
                        total_goods += float(goods_count)
                    except:
                        pass
                
                # 物流公司统计
                logistics = stockout.get('logistics_name', '未知')
                logistics_stats[logistics] = logistics_stats.get(logistics, 0) + 1
                
                # 仓库统计
                warehouse = stockout.get('warehouse_name', '未知')
                warehouse_stats[warehouse] = warehouse_stats.get(warehouse, 0) + 1
            
            # 写入统计数据
            stats_data = [
                ('查询日期', today.strftime('%Y-%m-%d'), '数据查询日期'),
                ('导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '文件生成时间'),
                ('API总记录数', 476, 'API返回的总记录数'),
                ('实际获取记录数', len(all_records), '实际获取的总记录数'),
                ('已发货订单数', len(all_stockouts), '状态为95的订单数'),
                ('已发货比例', f'{len(all_stockouts)/len(all_records)*100:.1f}%' if all_records else '0%', '已发货订单占比'),
                ('订单总金额', f'{total_amount:.2f}', '已发货订单总金额'),
                ('商品总数量', f'{total_goods:.0f}', '已发货商品总数量'),
                ('查询页数', page_no, '实际查询的页数'),
                ('主要物流公司', max(logistics_stats.items(), key=lambda x: x[1])[0] if logistics_stats else '无', '使用最多的物流公司'),
                ('主要发货仓库', max(warehouse_stats.items(), key=lambda x: x[1])[0] if warehouse_stats else '无', '发货最多的仓库')
            ]
            
            for row_idx, (item, value, desc) in enumerate(stats_data, 2):
                ws_stats.cell(row=row_idx, column=1, value=item)
                ws_stats.cell(row=row_idx, column=2, value=str(value))
                ws_stats.cell(row=row_idx, column=3, value=desc)
            
            # 自动调整列宽
            for ws in [ws_shipped, ws_stats]:
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width
            
            wb.save(filename)
            
            print(f"✅ 导出成功！")
            print(f"📊 已发货订单: {len(all_stockouts)} 条")
            print(f"💰 订单总金额: {total_amount:.2f}")
            print(f"📦 商品总数量: {total_goods:.0f}")
            print(f"📁 文件路径: {filename}")
            
            # 显示物流公司分布
            if logistics_stats:
                print(f"\n🚚 物流公司分布（前5名）:")
                for logistics, count in sorted(logistics_stats.items(), key=lambda x: x[1], reverse=True)[:5]:
                    print(f"   {logistics}: {count} 单")
            
            # 显示仓库分布
            if warehouse_stats:
                print(f"\n🏪 发货仓库分布（前5名）:")
                for warehouse, count in sorted(warehouse_stats.items(), key=lambda x: x[1], reverse=True)[:5]:
                    print(f"   {warehouse}: {count} 单")
            
            print(f"\n🎉 强制获取已发货订单完成！")
            
            if len(all_records) >= 400:
                print(f"✅ 成功获取了大部分数据！")
            else:
                print(f"⚠️ 获取的数据可能不完整")
                print(f"💡 建议：联系旺店通技术支持确认API的完整使用方法")
            
        except ImportError:
            print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
        except Exception as e:
            print(f"❌ 导出失败: {e}")
    else:
        print("❌ 没有获取到任何已发货订单")

if __name__ == "__main__":
    force_get_all_476()
