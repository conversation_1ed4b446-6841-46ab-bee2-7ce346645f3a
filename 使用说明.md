# 旺店通WMS销售出库单查询工具使用说明

## 项目概述

本项目是一个完整的Python工具包，用于查询旺店通WMS系统的销售出库单数据。支持多种查询条件、数据导出和统计分析功能。

## 安装和配置

### 1. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 2. 配置API信息

复制配置模板文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入您的旺店通API配置信息：
```env
WDT_SID=changhe
WDT_APP_KEY=changhe_zy_wdt
WDT_APP_SECRET=12684d858891369835a8ab7aa3bc660f
WDT_API_URL=https://openapi.wdtwms.com/open_api/service.php
```

## 使用方法

### 方法一：命令行工具

#### 基础用法

```bash
# 查询最近1小时的销售出库单
python main.py

# 查询最近24小时的数据
python main.py --hours 24

# 查询最近7天的数据
python main.py --days 7

# 查询指定日期范围
python main.py --start-date 2024-01-01 --end-date 2024-01-02
```

#### 高级查询

```bash
# 查询特定仓库的出库单
python main.py --warehouse-no "wdtapi3-test2" --hours 24

# 查询特定状态的出库单
python main.py --status-type 3 --status "110" --hours 24

# 查询特定店铺的出库单
python main.py --shop-nos "shop1,shop2" --hours 24

# 查询特定出库单号
python main.py --stockout-no "CK202505095"

# 查询特定订单号
python main.py --order-no "JY202505090021"

# 查询特定物流单号
python main.py --logistics-no "2025000501321"
```

#### 数据导出

```bash
# 导出为Excel文件
python main.py --hours 24 --output-format excel

# 导出为CSV文件
python main.py --hours 24 --output-format csv

# 导出为JSON文件
python main.py --hours 24 --output-format json

# 指定输出文件名
python main.py --hours 24 --output-format excel --output-file "销售出库单_20240101.xlsx"
```

#### 其他选项

```bash
# 显示统计信息
python main.py --hours 24 --stats

# 返回SN信息
python main.py --hours 24 --need-sn

# 获取主播信息
python main.py --hours 24 --get-anchor

# 设置分页大小
python main.py --hours 24 --page-size 200

# 限制最大页数
python main.py --hours 24 --max-pages 5

# 设置日志级别
python main.py --hours 24 --log-level DEBUG
```

### 方法二：Python代码调用

#### 基础查询示例

```python
from sales_stockout import SalesStockoutQuery
from datetime import datetime, timedelta

# 创建查询器
query = SalesStockoutQuery()

# 查询最近1小时的销售出库单
end_time = datetime.now()
start_time = end_time - timedelta(hours=1)

response = query.query_sales_stockout(
    start_time=start_time,
    end_time=end_time,
    status_type=0,  # 延时发货&已完成
    page_size=100
)

orders = response.get('data', {}).get('order', [])
print(f"查询到 {len(orders)} 条出库单")
```

#### 数据导出示例

```python
from data_processor import SalesStockoutProcessor

# 创建数据处理器
processor = SalesStockoutProcessor()

# 导出到Excel
excel_file = processor.export_to_excel(orders)
print(f"Excel文件已导出: {excel_file}")

# 获取统计信息
stats = processor.get_summary_statistics(orders)
print("统计信息:", stats)
```

#### 自定义客户端示例

```python
from wdt_client import WDTClient
from sales_stockout import SalesStockoutQuery

# 使用自定义配置创建客户端
client = WDTClient(
    sid="your_sid",
    app_key="your_app_key",
    app_secret="your_app_secret"
)

# 使用自定义客户端创建查询器
query = SalesStockoutQuery(client)
```

## 参数说明

### 状态类型 (status_type)

- `0`: 延时发货&已完成 (默认)
- `1`: 已取消
- `2`: 待分配~延时发货
- `3`: 按指定status查询

### 出库单状态码 (status)

| 状态码 | 状态名称 |
|--------|----------|
| 5 | 已取消 |
| 50 | 待审核 |
| 51 | 缺货 |
| 52 | 缺货待入库 |
| 60 | 待分配 |
| 70 | 待发货 |
| 75 | 待拣货 |
| 77 | 拣货中 |
| 79 | 已拣货 |
| 90 | 延时发货 |
| 110 | 已完成 |

## 输出格式

### Excel格式
- 包含所有字段的详细数据
- 自动格式化时间和数值
- 适合数据分析和报表

### CSV格式
- 纯文本格式，兼容性好
- 可用Excel或其他工具打开
- 适合数据导入和处理

### JSON格式
- 保持原始数据结构
- 适合程序处理和API对接

### 控制台输出
- 简洁的摘要信息
- 适合快速查看和调试

## 注意事项

1. **时间限制**: API要求单次查询的时间跨度不超过60分钟
2. **频率限制**: 请遵守API调用频率限制，避免过于频繁的请求
3. **数据权限**: 接口只返回自有平台及线下平台的隐私数据
4. **分页查询**: 建议单次查询的page_size不超过200

## 错误处理

- 程序包含完整的错误处理机制
- 网络错误会自动重试
- 详细的错误日志记录在 `wdt_api.log` 文件中

## 文件说明

- `main.py`: 命令行工具主程序
- `example.py`: 使用示例代码
- `config.py`: 配置管理
- `wdt_client.py`: API客户端
- `wdt_signature.py`: API签名算法
- `sales_stockout.py`: 销售出库单查询功能
- `data_processor.py`: 数据处理和导出
- `output/`: 导出文件存放目录（自动创建）

## 技术支持

如有问题，请查看：
1. 程序日志文件 `wdt_api.log`
2. 旺店通官方API文档
3. 项目README.md文件
