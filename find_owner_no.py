#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从出库单数据中查找正确的货主编号
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def find_owner_no():
    """从出库单数据中查找正确的货主编号"""
    
    print("🔍 从出库单数据中查找正确的货主编号")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print("📦 获取出库单数据，查找货主编号...")
    
    try:
        response = client.call_api('stockout.query', {
            "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 10,
            "page_no": 0
        })
        
        if response and 'content' in response:
            content = response.get('content', [])
            if isinstance(content, list) and content:
                print(f"✅ 获取到 {len(content)} 条出库单数据")
                
                # 分析第一条数据的所有字段，查找货主相关字段
                first_stockout = content[0]
                print(f"\n📋 第一条出库单的所有字段:")
                
                owner_fields = []
                for key, value in first_stockout.items():
                    if 'owner' in key.lower() or 'no' in key.lower():
                        owner_fields.append((key, value))
                        print(f"  🔍 可能的货主字段 {key}: {value}")
                    else:
                        print(f"    {key}: {value}")
                
                print(f"\n🔍 找到的可能货主字段:")
                for key, value in owner_fields:
                    print(f"  {key}: {value}")
                
                # 尝试使用找到的货主编号测试接口
                if owner_fields:
                    print(f"\n🔍 使用找到的货主编号测试 goods.spec.query.step 接口...")
                    
                    for key, value in owner_fields:
                        if value and str(value).strip():
                            print(f"\n📋 测试货主编号: {key} = {value}")
                            
                            try:
                                test_response = client.call_api('goods.spec.query.step', {
                                    "owner_no": str(value),
                                    "page_size": 5,
                                    "page_no": 0
                                })
                                
                                if test_response and 'content' in test_response:
                                    test_content = test_response.get('content', [])
                                    test_total = test_response.get('total', 0)
                                    
                                    print(f"✅ 成功！货主编号 {value} 可用")
                                    print(f"📊 返回数据: total={test_total}, content数量={len(test_content) if isinstance(test_content, list) else 'N/A'}")
                                    
                                    if isinstance(test_content, list) and test_content:
                                        first_goods = test_content[0]
                                        print(f"\n📋 第一条商品数据的字段:")
                                        
                                        # 查找货品编号和备注字段
                                        goods_no_fields = []
                                        remark_fields = []
                                        
                                        for goods_key, goods_value in first_goods.items():
                                            if 'goods_no' in goods_key.lower() or 'product_no' in goods_key.lower():
                                                goods_no_fields.append((goods_key, goods_value))
                                                print(f"  🔍 货品编号字段 {goods_key}: {goods_value}")
                                            elif 'remark' in goods_key.lower() or 'note' in goods_key.lower() or 'memo' in goods_key.lower():
                                                remark_fields.append((goods_key, goods_value))
                                                print(f"  📝 备注字段 {goods_key}: {goods_value}")
                                            else:
                                                print(f"    {goods_key}: {goods_value}")
                                        
                                        # 保存成功的结果
                                        success_data = {
                                            'owner_no': str(value),
                                            'owner_field': key,
                                            'api_response': test_response,
                                            'goods_no_fields': goods_no_fields,
                                            'remark_fields': remark_fields,
                                            'sample_goods': first_goods
                                        }
                                        
                                        success_filename = f"goods_spec_success_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                                        with open(success_filename, 'w', encoding='utf-8') as f:
                                            json.dump(success_data, f, ensure_ascii=False, indent=2)
                                        
                                        print(f"\n📁 成功数据已保存到: {success_filename}")
                                        print(f"\n🎉 找到正确的货主编号: {value}")
                                        print(f"✅ 货品编号字段: {[field[0] for field in goods_no_fields]}")
                                        print(f"✅ 备注字段: {[field[0] for field in remark_fields]}")
                                        
                                        return str(value), goods_no_fields, remark_fields
                                    
                                    else:
                                        print(f"❌ 返回数据为空")
                                
                                else:
                                    print(f"❌ API调用失败或返回格式错误")
                            
                            except Exception as e:
                                print(f"❌ 测试失败: {e}")
                
                else:
                    print(f"❌ 未找到货主相关字段")
            
            else:
                print(f"❌ 出库单数据为空")
        
        else:
            print(f"❌ 获取出库单数据失败")
    
    except Exception as e:
        print(f"❌ 查找货主编号失败: {e}")
    
    print(f"\n📝 总结:")
    print(f"如果上述方法都失败了，建议:")
    print(f"1. 检查旺店通WMS系统中的货主设置")
    print(f"2. 联系旺店通技术支持确认正确的货主编号")
    print(f"3. 查看API文档中关于货主编号的说明")
    
    return None, [], []

if __name__ == "__main__":
    find_owner_no()
