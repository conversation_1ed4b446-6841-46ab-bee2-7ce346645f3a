#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试货品编号字段和数据完整性
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def debug_goods_fields():
    """调试货品编号字段和数据完整性"""
    
    print("🔍 调试货品编号字段和数据完整性")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    
    # 查询参数
    params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 10,  # 只取10条数据用于调试
        "page_no": 0
    }
    
    print(f"🔍 获取样本数据进行字段分析...")
    
    try:
        response = client.call_api('stockout.query', params)
        
        if not response:
            print("❌ API响应为空")
            return
        
        content = response.get('content', [])
        total = response.get('total', 0)
        
        print(f"📊 API返回: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
        
        # 处理数据
        page_data = []
        if isinstance(content, list):
            page_data = content
        elif isinstance(content, dict):
            page_data = content.get('content', [])
            if not isinstance(page_data, list):
                page_data = [page_data] if page_data else []
        
        if not page_data:
            print("📝 无数据")
            return
        
        print(f"\n📋 分析前 {len(page_data)} 条数据的字段结构...")
        
        # 分析第一条数据的完整结构
        if page_data:
            first_item = page_data[0]
            print(f"\n🔍 第一条出库单的完整字段:")
            print(f"出库单号: {first_item.get('stockout_no', 'N/A')}")
            print(f"状态: {first_item.get('status', 'N/A')}")
            print(f"所有字段: {list(first_item.keys())}")
            
            # 重点分析商品明细字段
            goods_detail = first_item.get('goods_detail', [])
            print(f"\n📦 商品明细字段分析:")
            print(f"goods_detail类型: {type(goods_detail)}")
            print(f"goods_detail长度: {len(goods_detail) if isinstance(goods_detail, list) else 'N/A'}")
            
            if isinstance(goods_detail, list) and goods_detail:
                first_goods = goods_detail[0]
                print(f"\n🛍️ 第一个商品的所有字段:")
                for key, value in first_goods.items():
                    print(f"  {key}: {value}")
                
                # 检查可能的货品编号字段
                possible_goods_no_fields = ['goods_no', 'goods_code', 'product_no', 'product_code', 'item_no', 'item_code', 'sku_no', 'sku_code']
                print(f"\n🔍 检查可能的货品编号字段:")
                for field in possible_goods_no_fields:
                    if field in first_goods:
                        print(f"  ✅ {field}: {first_goods.get(field, 'N/A')}")
                    else:
                        print(f"  ❌ {field}: 不存在")
        
        # 统计所有状态
        status_stats = {}
        shipped_count = 0
        
        for item in page_data:
            status = str(item.get('status', ''))
            status_stats[status] = status_stats.get(status, 0) + 1
            if status == '95':
                shipped_count += 1
        
        print(f"\n📊 样本数据状态分布:")
        for status, count in status_stats.items():
            status_name = get_status_name(status)
            print(f"  状态 {status}({status_name}): {count} 条")
        
        print(f"\n🚛 已发货订单: {shipped_count} 条")
        
        # 检查是否有更多数据
        if total > len(page_data):
            print(f"\n⚠️ 注意: API显示总数为 {total} 条，但只返回了 {len(page_data)} 条数据")
            print(f"可能需要调整查询策略来获取所有 {total} 条数据")
        
        # 保存调试数据到文件
        debug_filename = f"debug_stockout_data_{today.strftime('%Y%m%d')}.json"
        with open(debug_filename, 'w', encoding='utf-8') as f:
            json.dump({
                'total': total,
                'returned_count': len(page_data),
                'sample_data': page_data[:2],  # 只保存前2条作为样本
                'status_stats': status_stats
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 调试数据已保存到: {debug_filename}")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")

def get_status_name(status):
    """获取状态名称"""
    status_map = {
        '5': '已取消',
        '10': '待放回',
        '50': '待审核',
        '51': '缺货',
        '52': '缺货待入库',
        '53': 'WMS已接单',
        '54': '获取电子面单',
        '58': '档口锁定',
        '60': '待分配',
        '61': '排队中',
        '63': '待补货',
        '65': '待处理',
        '70': '待发货',
        '73': '爆款锁定',
        '75': '待拣货',
        '77': '拣货中',
        '79': '已拣货',
        '90': '延时发货',
        '95': '已发货',
        '110': '已完成',
        '-1': '未发货'
    }
    return status_map.get(status, f'未知状态')

if __name__ == "__main__":
    debug_goods_fields()
