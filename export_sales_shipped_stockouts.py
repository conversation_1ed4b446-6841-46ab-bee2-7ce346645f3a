#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专用脚本导出已发货的销售出库单（自动过滤非销售出库单）
只导出当天数据
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def export_sales_shipped_stockouts():
    # 配置查询参数
    params = {
        "start_consign_time": None,  # 将在代码中设置
        "end_consign_time": None,    # 将在代码中设置
        "page_no": 0,
        "page_size": 100,
        "status": "95",             # 已发货状态
        "src_order_type": "1"       # 销售出库单类型
    }

    print("🚛 正在导出已发货的销售出库单")
    print("="*50)
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    params["start_consign_time"] = start_time.strftime('%Y-%m-%d %H:%M:%S')
    params["end_consign_time"] = end_time.strftime('%Y-%m-%d %H:%M:%S')

    print(f"📅 日期范围: {start_time} 至 {end_time}")
    print(f"🔍 筛选条件: 销售出库单(src_type=1) | 已发货状态(status=95)")

    client = WDTPostClient()
    all_stockouts = []
    page_no = 0
    
    # 先获取总记录数
    print("🔍 正在获取数据总数...")
    first_page = client.call_api('stockout.query', {
        **params,
        "page_no": 0,
        "page_size": 1
    })
    
    if not first_page or 'content' not in first_page:
        print("❌ 无法获取数据总数")
        return
        
    try:
        total_records = int(first_page.get('total', 0))
    except (ValueError, TypeError):
        print("⚠️ 获取总记录数失败，使用默认值0")
        total_records = 0
        
    if total_records == 0:
        print("📝 没有可用的出库单数据")
        return
        
    print(f"📊 总共需要获取 {total_records} 条出库单")
    
    # 计算总页数
    try:
        total_pages = (total_records + params["page_size"] - 1) // params["page_size"]
    except Exception as e:
        print(f"❌ 计算总页数失败: {str(e)}")
        return
    print(f"📑 总共需要查询 {total_pages} 页数据")
    
    # 分页获取所有数据
    page = 0
    max_retries = 3
    consecutive_empty = 0

    while page < total_pages:
        print(f"⏳ 正在获取第 {page + 1}/{total_pages} 页数据...")
        retry_count = 0
        success = False

        while retry_count < max_retries and not success:
            try:
                response = client.call_api('stockout.query', {
                    **params,
                    "page_no": page
                })

                if not response:
                    print(f"⚠️ 第 {page + 1} 页获取空响应，重试 {retry_count + 1}/{max_retries}")
                    retry_count += 1
                    continue

                # 安全获取响应内容
                content = response.get('content', [])

                if not content:
                    print(f"⚠️ 第 {page + 1} 页内容为空")
                    consecutive_empty += 1
                    if consecutive_empty >= 3:
                        print("🏁 连续3页无数据，终止查询")
                        break
                    success = True
                    page += 1
                    continue

                # 重置连续空页计数器
                consecutive_empty = 0

                # 解析数据列表
                data_list = []
                if isinstance(content, list):
                    data_list = content
                elif isinstance(content, dict):
                    data_list = content.get('content', [])
                    if not isinstance(data_list, list):
                        data_list = [data_list] if data_list else []

                print(f"📦 第 {page + 1} 页获取到 {len(data_list)} 条数据")

                # 验证并添加数据
                valid_stockouts = []
                for item in data_list:
                    if isinstance(item, dict) and str(item.get('status')) == '95':
                        valid_stockouts.append(item)

                if valid_stockouts:
                    all_stockouts.extend(valid_stockouts)
                    print(f"✅ 第 {page + 1} 页有效数据: {len(valid_stockouts)} 条")
                    print(f"📊 累计总数: {len(all_stockouts)} 条记录")
                else:
                    print(f"⚠️ 第 {page + 1} 页无有效数据")

                success = True

            except Exception as e:
                print(f"❌ 第 {page + 1} 页查询异常: {str(e)}")
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"⚠️ 第 {page + 1} 页达到最大重试次数，跳过")
                    break
                continue

        if not success and consecutive_empty >= 3:
            break

        page += 1

    # 导出到Excel
    if all_stockouts:
        filename = f"销售出库单_已发货_{today.strftime('%Y%m%d')}.xlsx"
        print(f"\n💾 正在导出到: {filename}")
        
        from openpyxl import Workbook
        wb = Workbook()
        ws = wb.active
        ws.title = "销售出库单"
        
        # 写入表头
        headers = ["出库单号", "订单号", "仓库", "发货时间", "物流单号", "商品数量"]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # 写入数据
        for row_idx, stockout in enumerate(all_stockouts, 2):
            ws.cell(row=row_idx, column=1, value=stockout.get("stockout_no"))
            ws.cell(row=row_idx, column=2, value=stockout.get("src_order_no"))
            ws.cell(row=row_idx, column=3, value=stockout.get("warehouse_name"))
            ws.cell(row=row_idx, column=4, value=stockout.get("consign_time"))
            ws.cell(row=row_idx, column=5, value=stockout.get("logistics_no"))
            ws.cell(row=row_idx, column=6, value=stockout.get("goods_count"))
        
        wb.save(filename)
        print(f"✅ 导出完成！共导出{len(all_stockouts)}条记录")
    else:
        print("⚠️ 未找到符合条件的出库单")

if __name__ == "__main__":
    export_sales_shipped_stockouts()
