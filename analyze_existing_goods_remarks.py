#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析现有货品档案中的备注信息
检查已有的货品档案文件中是否有备注
"""

import json
import os
from datetime import datetime

def analyze_existing_goods_remarks():
    """分析现有货品档案中的备注信息"""
    
    print("📝 分析现有货品档案中的备注信息")
    print("=" * 60)
    
    # 查找所有货品档案文件
    archive_files = []
    for filename in os.listdir('.'):
        if ('goods_archive' in filename or 'complete_goods' in filename) and filename.endswith('.json'):
            archive_files.append(filename)
    
    if not archive_files:
        print("❌ 未找到货品档案文件")
        return
    
    print(f"📁 找到 {len(archive_files)} 个货品档案文件:")
    for i, filename in enumerate(archive_files, 1):
        print(f"  {i}. {filename}")
    
    # 分析每个文件
    all_goods = {}
    goods_with_remarks = {}
    
    for filename in archive_files:
        print(f"\n🔍 分析文件: {filename}")
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                goods_data = json.load(f)
            
            print(f"📦 文件包含 {len(goods_data)} 个商品")
            
            file_remarks = 0
            for spec_no, item in goods_data.items():
                # 合并到总数据中
                if spec_no not in all_goods:
                    all_goods[spec_no] = item
                
                # 检查各种可能的备注字段
                remark_fields = [
                    'remark', 'description', 'memo', 'note', 
                    'goods_remark', 'spec_remark', 'product_remark',
                    'comment', 'remarks', 'desc'
                ]
                
                found_remark = ''
                remark_field = ''
                
                # 检查直接字段
                for field in remark_fields:
                    value = item.get(field, '')
                    if value and str(value).strip():
                        found_remark = str(value).strip()
                        remark_field = field
                        break
                
                # 如果没找到，检查full_data中的字段
                if not found_remark and 'full_data' in item:
                    full_data = item['full_data']
                    if isinstance(full_data, dict):
                        for field in remark_fields:
                            value = full_data.get(field, '')
                            if value and str(value).strip():
                                found_remark = str(value).strip()
                                remark_field = f"full_data.{field}"
                                break
                
                if found_remark:
                    goods_with_remarks[spec_no] = {
                        'spec_no': spec_no,
                        'goods_no': item.get('goods_no', ''),
                        'goods_name': item.get('goods_name', ''),
                        'spec_name': item.get('spec_name', ''),
                        'remark': found_remark,
                        'remark_field': remark_field,
                        'source_file': filename,
                        'full_item': item
                    }
                    file_remarks += 1
                    print(f"  📝 找到备注: {spec_no} - {found_remark[:50]}...")
            
            print(f"📊 该文件中有备注的商品: {file_remarks} 个")
            
        except Exception as e:
            print(f"❌ 分析文件 {filename} 失败: {e}")
    
    # 总体统计
    total_goods = len(all_goods)
    total_remarks = len(goods_with_remarks)
    coverage_rate = total_remarks / total_goods * 100 if total_goods > 0 else 0
    
    print(f"\n📊 总体统计:")
    print(f"📦 总商品数: {total_goods}")
    print(f"📝 有备注的商品: {total_remarks}")
    print(f"📈 备注覆盖率: {coverage_rate:.1f}%")
    
    if total_remarks == 0:
        print(f"\n❌ 所有货品档案中都没有找到备注信息")
        print(f"💡 可能的原因:")
        print(f"1. 货品档案中确实没有设置备注")
        print(f"2. 备注字段名称不是我们检查的这些")
        print(f"3. 备注信息在其他接口或字段中")
        
        # 显示一些商品的完整字段结构
        print(f"\n🔍 显示几个商品的完整字段结构:")
        count = 0
        for spec_no, item in all_goods.items():
            if count >= 3:
                break
            print(f"\n商品 {count + 1}: {spec_no}")
            print(f"  goods_name: {item.get('goods_name', '')}")
            
            # 显示所有字段
            if 'full_data' in item and isinstance(item['full_data'], dict):
                print(f"  所有字段:")
                for key, value in item['full_data'].items():
                    if isinstance(value, str) and len(value) > 50:
                        print(f"    {key}: {value[:50]}...")
                    else:
                        print(f"    {key}: {value}")
            count += 1
        
        return None
    
    # 如果找到了备注，进行详细分析
    print(f"\n📋 备注字段类型统计:")
    field_stats = {}
    for item in goods_with_remarks.values():
        field = item.get('remark_field', 'unknown')
        field_stats[field] = field_stats.get(field, 0) + 1
    
    for field, count in sorted(field_stats.items(), key=lambda x: x[1], reverse=True):
        print(f"  {field}: {count} 个商品")
    
    # 显示备注示例
    print(f"\n📝 备注内容示例:")
    count = 0
    for spec_no, item in goods_with_remarks.items():
        if count >= 10:
            break
        remark = item.get('remark', '')
        goods_name = item.get('goods_name', '')
        goods_no = item.get('goods_no', '')
        print(f"  {count+1}. {goods_no} ({goods_name})")
        print(f"     备注: {remark}")
        count += 1
    
    # 导出有备注的商品到Excel
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        
        filename = f"现有货品备注信息_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        print(f"\n📊 导出备注信息到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 设置边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 创建备注信息表
        ws = wb.active
        ws.title = "货品备注信息"
        
        # 表头
        headers = [
            '货品编号(goods_no)', '规格编号(spec_no)', '商品名称', '规格名称',
            '货品备注', '备注字段', '数据来源文件'
        ]
        
        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = thin_border
        
        # 写入数据
        for row_idx, (spec_no, item) in enumerate(goods_with_remarks.items(), 2):
            row_data = [
                item.get('goods_no', ''),
                spec_no,
                item.get('goods_name', ''),
                item.get('spec_name', ''),
                item.get('remark', ''),
                item.get('remark_field', ''),
                item.get('source_file', '')
            ]
            
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border
                cell.alignment = Alignment(vertical="center")
                
                # 高亮备注列
                if col_idx == 5:  # 备注列
                    cell.fill = PatternFill(start_color="FFFACD", end_color="FFFACD", fill_type="solid")
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 80)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # 创建统计表
        ws_stats = wb.create_sheet("统计信息")
        
        stats_data = [
            ['统计项目', '数值', '说明'],
            ['分析时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '数据分析的时间'],
            ['分析文件数', len(archive_files), '分析的货品档案文件数量'],
            ['总商品数', total_goods, '所有文件中的商品总数'],
            ['有备注的商品数', total_remarks, '包含备注信息的商品数量'],
            ['备注覆盖率', f'{coverage_rate:.1f}%', '有备注商品占总商品的比例']
        ]
        
        for row_idx, row_data in enumerate(stats_data, 1):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws_stats.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border
                if row_idx == 1:  # 表头
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                else:
                    cell.alignment = Alignment(vertical="center")
        
        # 调整统计表列宽
        ws_stats.column_dimensions['A'].width = 20
        ws_stats.column_dimensions['B'].width = 20
        ws_stats.column_dimensions['C'].width = 40
        
        # 保存文件
        wb.save(filename)
        
        print(f"✅ Excel导出成功！")
        print(f"📁 文件路径: {filename}")
        
    except ImportError:
        print("❌ 缺少openpyxl库，无法导出Excel")
    except Exception as e:
        print(f"❌ Excel导出失败: {e}")
    
    # 保存备注数据到JSON
    remarks_filename = f"existing_goods_remarks_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(remarks_filename, 'w', encoding='utf-8') as f:
        json.dump(goods_with_remarks, f, ensure_ascii=False, indent=2)
    
    print(f"📁 备注数据已保存到: {remarks_filename}")
    
    return goods_with_remarks

if __name__ == "__main__":
    analyze_existing_goods_remarks()
