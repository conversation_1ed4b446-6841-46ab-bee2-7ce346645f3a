"""
旺店通API签名算法实现
"""
import hashlib
import time
import json
from typing import Dict, Any, Union


class WDTSignature:
    """旺店通API签名生成器"""
    
    @staticmethod
    def generate_timestamp() -> int:
        """
        生成时间戳
        旺店通要求：秒级时间戳减去 2012-01-01 00:00:00(1325347200)
        """
        current_timestamp = int(time.time())
        base_timestamp = 1325347200  # 2012-01-01 00:00:00
        return current_timestamp - base_timestamp
    
    @staticmethod
    def sort_params(params: Dict[str, Any]) -> str:
        """
        对参数进行排序并拼接
        按照key的字母顺序排序，然后拼接成key=value&key=value的格式
        """
        if not params:
            return ""
        
        # 过滤掉空值和None值
        filtered_params = {}
        for key, value in params.items():
            if value is not None and value != "":
                if isinstance(value, (dict, list)):
                    # 对于复杂类型，转换为JSON字符串
                    filtered_params[key] = json.dumps(value, separators=(',', ':'), ensure_ascii=False)
                else:
                    filtered_params[key] = str(value)
        
        # 按key排序
        sorted_keys = sorted(filtered_params.keys())
        
        # 拼接参数
        param_string = "&".join([f"{key}={filtered_params[key]}" for key in sorted_keys])
        
        return param_string
    
    @staticmethod
    def generate_sign(params: Dict[str, Any], app_secret: str) -> str:
        """
        生成签名
        
        Args:
            params: 请求参数字典
            app_secret: 应用密钥
            
        Returns:
            签名字符串
        """
        # 1. 对参数进行排序拼接
        param_string = WDTSignature.sort_params(params)
        
        # 2. 在参数字符串前后加上app_secret
        sign_string = f"{app_secret}{param_string}{app_secret}"
        
        # 3. 计算MD5
        md5_hash = hashlib.md5(sign_string.encode('utf-8')).hexdigest()
        
        # 4. 转换为大写
        return md5_hash.upper()
    
    @staticmethod
    def build_request_params(method: str, params: Dict[str, Any], pager: Dict[str, Any],
                           sid: str, app_key: str, app_secret: str, salt: str = '') -> Dict[str, Any]:
        """
        构建完整的请求参数

        Args:
            method: API方法名
            params: 业务参数
            pager: 分页参数
            sid: 卖家标识
            app_key: 应用Key
            app_secret: 应用密钥
            salt: 盐值

        Returns:
            完整的请求参数字典
        """
        # 构建基础参数
        request_params = {
            'sid': sid,
            'key': app_key,
            'method': method,
            'v': '1.0',
            'timestamp': WDTSignature.generate_timestamp(),
        }

        # 添加salt参数（如果有）
        if salt:
            request_params['salt'] = salt
        
        # 添加业务参数
        if params:
            request_params['params'] = params
        
        # 添加分页参数
        if pager:
            request_params['pager'] = pager
        
        # 生成签名
        sign = WDTSignature.generate_sign(request_params, app_secret)
        request_params['sign'] = sign
        
        return request_params
    
    @staticmethod
    def validate_timestamp(timestamp: int, tolerance: int = 120) -> bool:
        """
        验证时间戳是否在允许范围内
        
        Args:
            timestamp: 要验证的时间戳
            tolerance: 允许的时间差（秒），默认120秒
            
        Returns:
            是否有效
        """
        current_timestamp = WDTSignature.generate_timestamp()
        time_diff = abs(current_timestamp - timestamp)
        return time_diff <= tolerance
