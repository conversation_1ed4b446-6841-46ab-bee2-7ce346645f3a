#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
货品档案备注获取解决方案指南
由于API权限限制，提供多种解决方案
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def create_solution_guide():
    """创建货品档案备注获取解决方案指南"""
    
    print("📋 货品档案备注获取解决方案指南")
    print("=" * 60)
    
    # 先获取当前的商品数据作为示例
    client = WDTPostClient()
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print("📦 获取当前商品数据作为示例...")
    
    spec_nos = []
    goods_names = []
    
    try:
        response = client.call_api('stockout.query', {
            "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 10,
            "page_no": 0
        })
        
        if response and 'content' in response:
            content = response.get('content', [])
            if isinstance(content, list):
                for stockout in content:
                    goods_detail = stockout.get('goods_detail', [])
                    if isinstance(goods_detail, list):
                        for detail in goods_detail:
                            spec_no = detail.get('spec_no')
                            goods_name = detail.get('goods_name')
                            if spec_no and spec_no not in spec_nos:
                                spec_nos.append(spec_no)
                                goods_names.append(goods_name)
                                if len(spec_nos) >= 5:
                                    break
                    if len(spec_nos) >= 5:
                        break
        
        print(f"✅ 获取到 {len(spec_nos)} 个商品示例")
    except Exception as e:
        print(f"❌ 获取示例数据失败: {e}")
    
    # 创建解决方案文档
    solutions = {
        "问题描述": {
            "问题": "需要获取货品档案中的货品备注",
            "现状": "旺店通WMS API没有货品档案查询权限",
            "影响": "无法通过API直接获取货品备注信息"
        },
        
        "解决方案": {
            "方案1_联系技术支持": {
                "描述": "联系旺店通技术支持申请API权限",
                "步骤": [
                    "1. 联系旺店通客服或技术支持",
                    "2. 说明需要货品档案查询API权限",
                    "3. 提供账号信息: changhe",
                    "4. 说明业务需求: 需要获取货品备注信息",
                    "5. 可能需要的API接口: goods.query, spec.query"
                ],
                "联系方式": {
                    "官网": "https://www.wdtwms.com/",
                    "技术支持": "需要在官网查找具体联系方式",
                    "文档": "https://www.yuque.com/huice-wiki/bhxv6e"
                },
                "优点": "一劳永逸，可以直接通过API获取",
                "缺点": "需要等待审批，可能需要付费"
            },
            
            "方案2_手动导出关联": {
                "描述": "从WMS系统手动导出货品档案，然后通过程序关联",
                "步骤": [
                    "1. 登录旺店通WMS系统",
                    "2. 进入货品档案管理",
                    "3. 导出包含spec_no和备注的货品档案Excel",
                    "4. 使用Python程序读取Excel并关联数据"
                ],
                "实现方式": "创建货品档案映射表",
                "优点": "立即可用，成本低",
                "缺点": "需要定期手动更新，数据可能不是实时的"
            },
            
            "方案3_数据库直连": {
                "描述": "如果有数据库访问权限，直接查询货品档案表",
                "前提": "需要数据库访问权限",
                "步骤": [
                    "1. 获取旺店通WMS数据库连接权限",
                    "2. 查找货品档案相关表",
                    "3. 通过spec_no关联查询备注信息"
                ],
                "优点": "数据实时，查询灵活",
                "缺点": "需要数据库权限，技术要求高"
            },
            
            "方案4_临时替代方案": {
                "描述": "使用现有的remark字段作为临时方案",
                "说明": "出库单商品明细中的remark字段可能包含部分备注信息",
                "实现": "已在现有导出脚本中实现",
                "优点": "立即可用",
                "缺点": "可能不是完整的货品档案备注"
            }
        },
        
        "推荐实施顺序": [
            "1. 立即使用方案4（临时替代方案）满足当前需求",
            "2. 同时联系旺店通技术支持（方案1）申请API权限",
            "3. 如果方案1不可行，考虑方案2（手动导出关联）",
            "4. 长期考虑方案3（数据库直连）"
        ],
        
        "当前商品示例": []
    }
    
    # 添加商品示例
    for i, (spec_no, goods_name) in enumerate(zip(spec_nos, goods_names)):
        solutions["当前商品示例"].append({
            "序号": i + 1,
            "spec_no": spec_no,
            "goods_name": goods_name,
            "说明": "这些是当前出库单中的商品，需要获取它们的货品档案备注"
        })
    
    # 保存解决方案文档
    solution_filename = f"货品备注解决方案_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(solution_filename, 'w', encoding='utf-8') as f:
        json.dump(solutions, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 解决方案文档已保存到: {solution_filename}")
    
    # 显示解决方案摘要
    print(f"\n📋 解决方案摘要:")
    print(f"🎯 推荐方案: 联系旺店通技术支持申请API权限")
    print(f"⚡ 临时方案: 使用现有的remark字段")
    print(f"🔄 备选方案: 手动导出货品档案进行关联")
    
    print(f"\n📞 下一步行动:")
    print(f"1. 联系旺店通技术支持")
    print(f"   - 账号: changhe")
    print(f"   - 需求: 货品档案查询API权限")
    print(f"   - 接口: goods.query, spec.query")
    print(f"2. 同时使用现有的导出脚本获取部分备注信息")
    
    return solution_filename

def create_manual_mapping_template():
    """创建手动映射模板（方案2的实现）"""
    
    print(f"\n📋 创建手动映射模板...")
    
    # 创建货品档案映射模板
    template_filename = "货品档案映射模板.py"
    
    template_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
货品档案映射模板
用于手动导入货品档案数据，然后与出库单数据关联
"""

import pandas as pd
from datetime import datetime
from wdt_post_client import WDTPostClient

class GoodsArchiveMapper:
    """货品档案映射器"""
    
    def __init__(self, goods_archive_file=None):
        """
        初始化映射器
        
        Args:
            goods_archive_file: 货品档案Excel文件路径
        """
        self.goods_archive_file = goods_archive_file
        self.goods_mapping = {}
        
        if goods_archive_file:
            self.load_goods_archive()
    
    def load_goods_archive(self):
        """加载货品档案数据"""
        try:
            # 读取Excel文件
            df = pd.read_excel(self.goods_archive_file)
            
            # 假设Excel包含以下列：spec_no, goods_name, remark
            # 请根据实际的Excel结构调整列名
            required_columns = ['spec_no', 'goods_name', 'remark']
            
            for column in required_columns:
                if column not in df.columns:
                    print(f"警告: Excel文件中缺少列 '{column}'")
                    return
            
            # 创建映射字典
            for _, row in df.iterrows():
                spec_no = str(row['spec_no'])
                self.goods_mapping[spec_no] = {
                    'goods_name': row['goods_name'],
                    'remark': row['remark'] if pd.notna(row['remark']) else ''
                }
            
            print(f"✅ 成功加载 {len(self.goods_mapping)} 条货品档案数据")
            
        except Exception as e:
            print(f"❌ 加载货品档案失败: {e}")
    
    def get_goods_remark(self, spec_no):
        """
        根据spec_no获取货品备注
        
        Args:
            spec_no: 规格编号
            
        Returns:
            货品备注
        """
        spec_no = str(spec_no)
        if spec_no in self.goods_mapping:
            return self.goods_mapping[spec_no]['remark']
        else:
            return ''
    
    def export_stockout_with_archive_remark(self):
        """导出包含货品档案备注的出库单"""
        
        print("📦 导出包含货品档案备注的出库单...")
        
        # 获取出库单数据（复用现有逻辑）
        client = WDTPostClient()
        today = datetime.now()
        start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
        
        if end_time > datetime.now():
            end_time = datetime.now()
        
        # 这里可以复用之前的出库单查询逻辑
        # 然后在导出时，使用 self.get_goods_remark(spec_no) 获取备注
        
        print("请参考现有的导出脚本，在商品明细部分添加:")
        print("goods_archive_remark = mapper.get_goods_remark(detail.get('spec_no', ''))")
        
        return "需要集成到现有的导出脚本中"

# 使用示例
if __name__ == "__main__":
    # 1. 从WMS系统导出货品档案Excel文件
    # 2. 确保Excel包含 spec_no, goods_name, remark 列
    # 3. 使用以下代码加载和使用
    
    # mapper = GoodsArchiveMapper("货品档案.xlsx")
    # remark = mapper.get_goods_remark("1683214231569040384")
    # print(f"货品备注: {remark}")
    
    print("📋 使用步骤:")
    print("1. 从旺店通WMS系统导出货品档案Excel")
    print("2. 确保Excel包含 spec_no, goods_name, remark 列")
    print("3. 修改 goods_archive_file 路径")
    print("4. 集成到现有的导出脚本中")
'''
    
    with open(template_filename, 'w', encoding='utf-8') as f:
        f.write(template_code)
    
    print(f"📁 映射模板已创建: {template_filename}")
    
    return template_filename

if __name__ == "__main__":
    # 创建解决方案指南
    solution_file = create_solution_guide()
    
    # 创建手动映射模板
    template_file = create_manual_mapping_template()
    
    print(f"\n🎉 完成！")
    print(f"📋 解决方案文档: {solution_file}")
    print(f"🔧 映射模板: {template_file}")
    print(f"\n💡 建议立即行动:")
    print(f"1. 查看解决方案文档")
    print(f"2. 联系旺店通技术支持")
    print(f"3. 考虑使用手动映射方案作为临时解决方案")
