"""
销售出库单查询功能
"""
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
import logging

from wdt_client import WDTClient, WDTAPIException


class SalesStockoutQuery:
    """销售出库单查询类"""
    
    def __init__(self, client: WDTClient = None):
        """
        初始化查询器
        
        Args:
            client: WDT客户端实例，如果为None则创建新实例
        """
        self.client = client or WDTClient()
        self.logger = logging.getLogger(__name__)
    
    def query_sales_stockout(self, 
                           start_time: Union[str, datetime],
                           end_time: Union[str, datetime],
                           status_type: int = 0,
                           status: str = None,
                           warehouse_no: str = None,
                           stockout_no: str = None,
                           shop_nos: str = None,
                           src_order_no: str = None,
                           logistics_no: str = None,
                           need_sn: bool = False,
                           position: int = 0,
                           is_slave: bool = False,
                           get_anchor: bool = False,
                           order_type: int = 0,
                           need_pick_position: int = 0,
                           need_gov_subsidy_info: bool = False,
                           page_size: int = None,
                           page_no: int = 0,
                           calc_total: bool = True) -> Dict[str, Any]:
        """
        查询销售出库单
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            status_type: 出库单状态类型 (0:延时发货&已完成, 1:已取消, 2:待分配~延时发货, 3:按指定status查询)
            status: 出库单状态详细（status_type=3时使用）
            warehouse_no: 仓库编号
            stockout_no: 出库单号
            shop_nos: 店铺编号（多个用逗号分隔）
            src_order_no: 系统订单号
            logistics_no: 物流单号
            need_sn: 是否返回SN信息
            position: 是否按货位排序
            is_slave: 是否使用从库查询
            get_anchor: 是否获取主播信息
            order_type: 排序类型 (0:默认, 1:修改时间降序, 2:发货时间降序)
            need_pick_position: 是否需要拣货位明细
            need_gov_subsidy_info: 是否需要国补信息
            page_size: 分页大小
            page_no: 页码
            calc_total: 是否计算总数
            
        Returns:
            查询结果
        """
        # 格式化时间
        if isinstance(start_time, datetime):
            start_time = start_time.strftime('%Y-%m-%d %H:%M:%S')
        if isinstance(end_time, datetime):
            end_time = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        # 构建查询参数
        params = {
            'start_time': start_time,
            'end_time': end_time,
            'status_type': status_type
        }
        
        # 添加可选参数
        optional_params = {
            'status': status,
            'warehouse_no': warehouse_no,
            'stockout_no': stockout_no,
            'shop_nos': shop_nos,
            'src_order_no': src_order_no,
            'logistics_no': logistics_no,
            'need_sn': need_sn,
            'position': position,
            'is_slave': is_slave,
            'get_anchor': get_anchor,
            'order_type': order_type,
            'need_pick_position': need_pick_position,
            'need_gov_subsidy_info': need_gov_subsidy_info
        }
        
        for key, value in optional_params.items():
            if value is not None and value != '':
                params[key] = value
        
        try:
            response = self.client.call_api(
                method='wdt.wms.stockout.sales.querywithdetail',
                params=params,
                page_size=page_size,
                page_no=page_no,
                calc_total=calc_total
            )
            
            return response
            
        except WDTAPIException as e:
            self.logger.error(f"查询销售出库单失败: {e}")
            raise
    
    def query_sales_stockout_by_date_range(self,
                                         start_date: Union[str, datetime],
                                         end_date: Union[str, datetime],
                                         status_type: int = 0,
                                         **kwargs) -> List[Dict[str, Any]]:
        """
        按日期范围查询销售出库单（自动分割时间段）
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            status_type: 出库单状态类型
            **kwargs: 其他查询参数
            
        Returns:
            所有出库单数据列表
        """
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        
        all_data = []
        current_date = start_date
        
        # 按小时分割查询（API限制最大跨度60分钟）
        while current_date < end_date:
            # 计算当前查询的结束时间（最多60分钟）
            next_date = min(current_date + timedelta(hours=1), end_date)
            
            try:
                # 查询当前时间段的数据
                response = self.query_sales_stockout(
                    start_time=current_date,
                    end_time=next_date,
                    status_type=status_type,
                    **kwargs
                )
                
                data = response.get('data', {})
                orders = data.get('order', [])
                
                if orders:
                    all_data.extend(orders)
                    self.logger.info(f"查询时间段 {current_date} - {next_date}: 获取到 {len(orders)} 条记录")
                
            except WDTAPIException as e:
                self.logger.error(f"查询时间段 {current_date} - {next_date} 失败: {e}")
                # 继续查询下一个时间段
            
            current_date = next_date
        
        self.logger.info(f"总共获取到 {len(all_data)} 条销售出库单记录")
        return all_data
    
    def query_sales_stockout_all_pages(self,
                                     start_time: Union[str, datetime],
                                     end_time: Union[str, datetime],
                                     status_type: int = 0,
                                     page_size: int = None,
                                     max_pages: int = None,
                                     **kwargs) -> List[Dict[str, Any]]:
        """
        查询所有分页的销售出库单数据
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            status_type: 出库单状态类型
            page_size: 分页大小
            max_pages: 最大页数限制
            **kwargs: 其他查询参数
            
        Returns:
            所有页面的出库单数据列表
        """
        # 格式化时间
        if isinstance(start_time, datetime):
            start_time = start_time.strftime('%Y-%m-%d %H:%M:%S')
        if isinstance(end_time, datetime):
            end_time = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        # 构建查询参数
        params = {
            'start_time': start_time,
            'end_time': end_time,
            'status_type': status_type
        }
        params.update(kwargs)
        
        try:
            all_data = self.client.call_api_all_pages(
                method='wdt.wms.stockout.sales.querywithdetail',
                params=params,
                page_size=page_size,
                max_pages=max_pages
            )
            
            self.logger.info(f"获取到 {len(all_data)} 条销售出库单记录")
            return all_data
            
        except WDTAPIException as e:
            self.logger.error(f"查询所有分页数据失败: {e}")
            raise
    
    def get_stockout_status_name(self, status: int) -> str:
        """
        获取出库单状态名称
        
        Args:
            status: 状态码
            
        Returns:
            状态名称
        """
        status_map = {
            5: '已取消',
            10: '待放回',
            50: '待审核',
            51: '缺货',
            52: '缺货待入库',
            53: 'WMS已接单',
            54: '获取电子面单',
            58: '档口锁定',
            60: '待分配',
            61: '排队中',
            63: '待补货',
            65: '待处理',
            70: '待发货',
            73: '爆款锁定',
            75: '待拣货',
            77: '拣货中',
            79: '已拣货',
            90: '延时发货',
            110: '已完成',
            -1: '未发货'
        }
        
        return status_map.get(status, f'未知状态({status})')
