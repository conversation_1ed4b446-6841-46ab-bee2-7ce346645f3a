#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取当天销售订单并导出到Excel
"""

import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any
from wdt_post_client import WDTPostClient

class DailyOrderExporter:
    """当天订单导出器"""
    
    def __init__(self):
        """初始化导出器"""
        self.client = WDTPostClient()
        self.logger = logging.getLogger(__name__)
        
    def get_daily_orders(self, target_date: datetime = None) -> List[Dict[str, Any]]:
        """
        获取指定日期的所有销售订单
        
        Args:
            target_date: 目标日期，默认为今天
            
        Returns:
            订单列表
        """
        if target_date is None:
            target_date = datetime.now()
        
        # 设置查询时间范围（当天0点到23:59:59）
        start_time = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = target_date.replace(hour=23, minute=59, second=59, microsecond=0)
        
        # 如果是今天，结束时间不能超过当前时间
        if target_date.date() == datetime.now().date():
            current_time = datetime.now()
            if end_time > current_time:
                end_time = current_time
        
        print(f"📅 查询日期: {target_date.strftime('%Y-%m-%d')}")
        print(f"⏰ 查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        all_orders = []
        
        # 查询不同状态的订单
        status_map = {
            30: "待审核",
            5: "已取消"
        }
        
        for status_code, status_name in status_map.items():
            print(f"\n🔍 查询{status_name}订单...")
            
            try:
                page_no = 0
                page_size = 100  # 最大页面大小
                
                while True:
                    response = self.client.query_sales_trades(
                        start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                        end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                        trade_status=status_code,
                        page_no=page_no,
                        page_size=page_size
                    )
                    
                    total = response.get('total', 0)
                    trades = response.get('content', [])
                    
                    print(f"   📊 第{page_no + 1}页: {len(trades)} 条订单")
                    
                    if not trades:
                        break
                    
                    # 添加状态名称到每个订单
                    for trade in trades:
                        trade['status_name'] = status_name
                    
                    all_orders.extend(trades)
                    
                    # 如果当前页数据少于页面大小，说明已经是最后一页
                    if len(trades) < page_size:
                        break
                    
                    page_no += 1
                
                print(f"   ✅ {status_name}订单总计: {len([o for o in all_orders if o.get('status_name') == status_name])} 条")
                
            except Exception as e:
                print(f"   ❌ 查询{status_name}订单失败: {e}")
                self.logger.error(f"查询状态{status_code}订单失败: {e}")
        
        print(f"\n📈 总计获取订单: {len(all_orders)} 条")
        return all_orders
    
    def format_orders_for_excel(self, orders: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        格式化订单数据为Excel格式
        
        Args:
            orders: 订单列表
            
        Returns:
            格式化后的DataFrame
        """
        if not orders:
            return pd.DataFrame()
        
        formatted_data = []
        
        for order in orders:
            # 基础订单信息
            base_info = {
                'ERP单号': order.get('trade_no', ''),
                '原始单号': order.get('src_tids', ''),
                '仓储单号': order.get('src_order_no', ''),
                '订单状态': order.get('status_name', ''),
                '状态代码': order.get('trade_status', ''),
                '货主编号': order.get('owner_no', ''),
                '仓库编号': order.get('warehouse_no', ''),
                '店铺名称': order.get('shop_name', ''),
                '店铺编号': order.get('shop_no', ''),
                '物流编码': order.get('logistics_code', ''),
                '买家昵称': order.get('buyer_nick', ''),
                '收件人省': order.get('receiver_province', ''),
                '收件人市': order.get('receiver_city', ''),
                '收件人区': order.get('receiver_district', ''),
                '收件人地址': order.get('receiver_area', ''),
                '订单总金额': order.get('total_amount', ''),
                '货品数量': order.get('goods_count', ''),
                '货品种类数': order.get('goods_type_count', ''),
                '预估重量': order.get('weight', ''),
                '订单体积': order.get('volume', ''),
                '客服备注': order.get('cs_remark', ''),
                '买家留言': order.get('buyer_message', ''),
                '平台下单时间': order.get('trade_time', ''),
                '付款时间': order.get('pay_time', ''),
                '接单时间': order.get('trade_create_time', ''),
                '平台名称': order.get('platform_name', ''),
                '订单类型': self._get_trade_type_name(order.get('trade_type', 0)),
                '发货条件': self._get_delivery_term_name(order.get('delivery_term', 1)),
                'COD金额': order.get('cod_amount', ''),
                '标记名称': order.get('flag_name', '')
            }
            
            # 商品明细
            goods_detail = order.get('goods_detail', [])
            if goods_detail:
                for i, goods in enumerate(goods_detail):
                    row = base_info.copy()
                    row.update({
                        '商品序号': i + 1,
                        '商品编码': goods.get('spec_no', ''),
                        '商品数量': goods.get('num', ''),
                        '商品重量': goods.get('weight', ''),
                        '商品体积': goods.get('volume', ''),
                        '商品备注': goods.get('remark', ''),
                        '行号': goods.get('orderline_no', '')
                    })
                    formatted_data.append(row)
            else:
                # 没有商品明细的订单
                formatted_data.append(base_info)
        
        return pd.DataFrame(formatted_data)
    
    def _get_trade_type_name(self, trade_type: int) -> str:
        """获取订单类型名称"""
        type_map = {
            0: "正常订单",
            1: "换货订单", 
            2: "补发订单"
        }
        return type_map.get(trade_type, f"未知类型({trade_type})")
    
    def _get_delivery_term_name(self, delivery_term: int) -> str:
        """获取发货条件名称"""
        term_map = {
            1: "非货到付款",
            2: "货到付款"
        }
        return term_map.get(delivery_term, f"未知条件({delivery_term})")
    
    def export_to_excel(self, orders: List[Dict[str, Any]], filename: str = None) -> str:
        """
        导出订单到Excel文件
        
        Args:
            orders: 订单列表
            filename: 文件名，默认自动生成
            
        Returns:
            生成的文件路径
        """
        if filename is None:
            today = datetime.now().strftime('%Y%m%d')
            filename = f"销售订单_{today}.xlsx"
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 格式化数据
        df = self.format_orders_for_excel(orders)
        
        if df.empty:
            print("⚠️ 没有数据可导出")
            return ""
        
        try:
            # 创建Excel写入器
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 写入主数据表
                df.to_excel(writer, sheet_name='销售订单明细', index=False)
                
                # 创建汇总表
                summary_df = self._create_summary(orders)
                summary_df.to_excel(writer, sheet_name='订单汇总', index=False)
                
                # 格式化Excel
                self._format_excel(writer, df)
            
            print(f"✅ 导出成功！")
            print(f"📊 总计导出: {len(df)} 行数据")
            print(f"📁 文件路径: {filename}")
            
            return filename
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            self.logger.error(f"Excel导出失败: {e}")
            return ""
    
    def _create_summary(self, orders: List[Dict[str, Any]]) -> pd.DataFrame:
        """创建订单汇总表"""
        if not orders:
            return pd.DataFrame()
        
        # 按状态统计
        status_summary = {}
        total_amount = 0
        total_goods_count = 0
        
        for order in orders:
            status = order.get('status_name', '未知')
            if status not in status_summary:
                status_summary[status] = {
                    '订单数量': 0,
                    '总金额': 0,
                    '总货品数': 0
                }
            
            status_summary[status]['订单数量'] += 1
            
            # 处理金额
            amount = order.get('total_amount', 0)
            if isinstance(amount, str):
                try:
                    amount = float(amount)
                except:
                    amount = 0
            status_summary[status]['总金额'] += amount
            total_amount += amount
            
            # 处理货品数量
            goods_count = order.get('goods_count', 0)
            if isinstance(goods_count, str):
                try:
                    goods_count = float(goods_count)
                except:
                    goods_count = 0
            status_summary[status]['总货品数'] += goods_count
            total_goods_count += goods_count
        
        # 构建汇总数据
        summary_data = []
        for status, data in status_summary.items():
            summary_data.append({
                '订单状态': status,
                '订单数量': data['订单数量'],
                '总金额': round(data['总金额'], 2),
                '总货品数': round(data['总货品数'], 2)
            })
        
        # 添加总计行
        summary_data.append({
            '订单状态': '总计',
            '订单数量': len(orders),
            '总金额': round(total_amount, 2),
            '总货品数': round(total_goods_count, 2)
        })
        
        return pd.DataFrame(summary_data)
    
    def _format_excel(self, writer, df: pd.DataFrame):
        """格式化Excel样式"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows
            
            # 获取工作表
            worksheet = writer.sheets['销售订单明细']
            
            # 设置标题行样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center")
            
            # 自动调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
            
        except Exception as e:
            self.logger.warning(f"Excel格式化失败: {e}")

def main():
    """主函数"""
    print("🎯 旺店通销售订单导出工具")
    print("=" * 50)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # 创建导出器
    exporter = DailyOrderExporter()
    
    try:
        # 获取当天订单
        orders = exporter.get_daily_orders()
        
        if not orders:
            print("\n📝 当天没有订单数据")
            return
        
        # 导出到Excel
        filename = exporter.export_to_excel(orders)
        
        if filename:
            print(f"\n🎉 导出完成！")
            print(f"📁 文件: {filename}")
            print(f"📊 数据: {len(orders)} 条订单")
        else:
            print("\n❌ 导出失败")
    
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        logging.error(f"程序执行失败: {e}")

if __name__ == "__main__":
    main()
