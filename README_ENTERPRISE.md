# 旺店通企业版WMS API集成

本项目提供了旺店通企业版WMS系统的销售出库单查询功能。

## 🎯 功能特性

- ✅ 旺店通企业版API签名算法实现
- ✅ 销售出库单查询功能
- ✅ 分页查询支持
- ✅ 时间分段查询（处理大时间跨度）
- ✅ 多种查询条件支持
- ✅ JSON格式数据导出
- ✅ 完整的错误处理和日志记录

## 📋 API版本说明

**重要**: 本项目现在支持旺店通企业版API，与旗舰版API有以下区别：

| 特性 | 企业版 | 旗舰版 |
|------|--------|--------|
| 签名算法 | 企业版专用算法 | 奇门算法 |
| API端点 | `https://api.wangdian.cn/openapi2/` | `https://openapi.wdtwms.com/open_api/service.php` |
| 时间戳格式 | 标准Unix时间戳 | 相对时间戳 |
| 接口权限 | 需要在开放平台申请 | 直接使用 |

## 🚀 快速开始

### 1. 环境配置

创建 `.env` 文件：

```env
# 旺店通企业版API配置
WDT_SID=your_sid
WDT_APP_KEY=your_app_key
WDT_APP_SECRET=your_app_secret
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 申请API权限

**重要步骤**: 在使用API之前，必须在旺店通开放平台申请接口权限：

1. 登录 [旺店通开放平台](https://open.wangdian.cn/qyb/open/welcome)
2. 进入 [应用管理](https://open.wangdian.cn/qyb/open/abut)
3. 点击 [新增] 申请 `stockout_order_query_trade.php` 接口权限
4. 等待审核通过

### 4. 测试连接

```bash
python test_enterprise_api.py
```

### 5. 查询销售出库单

```bash
# 查询最近1小时的出库单
python enterprise_main.py

# 查询指定日期范围
python enterprise_main.py --start-date 2024-01-01 --end-date 2024-01-02

# 查询已发货状态的出库单
python enterprise_main.py --status 95 --hours 24

# 导出到JSON文件
python enterprise_main.py --output stockout_data.json --hours 24
```

## 📖 API文档

### 销售出库单状态码

| 状态码 | 说明 |
|--------|------|
| 5 | 已取消 |
| 55 | 已审核 |
| 95 | 已发货 |
| 105 | 部分打款 |
| 110 | 已完成 |
| 113 | 异常发货 |

### 命令行参数

```bash
python enterprise_main.py [选项]

时间参数:
  --start-date YYYY-MM-DD    开始日期
  --end-date YYYY-MM-DD      结束日期
  --hours N                  查询最近N小时（默认1）

查询参数:
  --status STATUS            出库单状态
  --warehouse-no NO          仓库编号
  --shop-no NO               店铺编号

分页参数:
  --page-size SIZE           每页大小（1-100，默认100）
  --max-pages N              最大查询页数

输出参数:
  --output FILE              输出JSON文件
  --details-only             只输出简化详情

其他参数:
  --log-level LEVEL          日志级别
  --segment-hours N          时间分段小时数
```

## 🔧 编程接口

### 基本用法

```python
from datetime import datetime, timedelta
from enterprise_sales_stockout import EnterpriseSalesStockoutQuery

# 创建查询器
query = EnterpriseSalesStockoutQuery()

# 查询最近24小时的出库单
end_time = datetime.now()
start_time = end_time - timedelta(hours=24)

stockout_list = query.query_all_pages(
    start_time=start_time,
    end_time=end_time,
    status=95  # 已发货
)

print(f"找到 {len(stockout_list)} 条出库单")
```

### 高级用法

```python
# 分段查询大时间跨度
stockout_list = query.query_by_time_segments(
    start_time=datetime(2024, 1, 1),
    end_time=datetime(2024, 1, 31),
    segment_hours=24,
    status=110,  # 已完成
    warehouse_no="WH001"
)

# 提取简化详情
details = query.get_stockout_details(stockout_list)
```

## 🔍 签名算法验证

项目包含了完整的签名算法实现和验证：

```python
from wdt_enterprise_signature import WDTEnterpriseSignature

# 使用官方文档示例验证
params = {
    'appkey': 'test2-xx',
    'page_no': '0',
    'end_time': '2016-08-01 13:00:00',
    'start_time': '2016-08-01 12:00:00',
    'page_size': '40',
    'sid': 'test2',
    'timestamp': '1470042310'
}

sign = WDTEnterpriseSignature.generate_sign(params, '12345')
print(sign)  # 输出: ad4e6fe037ea6e3ba4768317be9d1309
```

## 📁 项目结构

```
WMSAPI/
├── wdt_enterprise_signature.py    # 企业版签名算法
├── wdt_enterprise_client.py       # 企业版API客户端
├── enterprise_sales_stockout.py   # 销售出库单查询
├── enterprise_main.py             # 企业版主程序
├── test_enterprise_api.py         # 企业版测试脚本
├── config.py                      # 配置管理
├── requirements.txt               # 依赖列表
├── .env                          # 环境变量
└── README_ENTERPRISE.md          # 企业版文档
```

## ⚠️ 注意事项

1. **API权限**: 必须在旺店通开放平台申请接口权限
2. **时间跨度**: 单次查询最大30天，超过会自动分段查询
3. **测试环境**: 测试环境时间跨度限制为60分钟
4. **分页限制**: 每页最大100条记录
5. **签名算法**: 企业版使用专用签名算法，与旗舰版不同

## 🔗 相关链接

- [旺店通开放平台](https://open.wangdian.cn/qyb/open/welcome)
- [API文档](https://open.wangdian.cn/qyb/open/apidoc)
- [签名算法说明](https://open.wangdian.cn/open/guide?path=guide_signsf)
- [销售出库单查询接口](https://open.wangdian.cn/qyb/open/apidoc/doc?path=stockout_order_query_trade.php)

## 🐛 故障排除

### 常见错误

1. **权限错误 (1090)**
   ```
   无接口调用权限，请于开放平台[应用管理]中点击[新增]申请权限
   ```
   解决方案: 在开放平台申请接口权限

2. **时间格式错误 (2102)**
   ```
   start_time和end_time为空或不是有效的时间格式
   ```
   解决方案: 使用正确的时间格式 `YYYY-MM-DD HH:MM:SS`

3. **签名错误**
   ```
   签名验证失败
   ```
   解决方案: 检查APP_SECRET是否正确

## 📞 技术支持

如有问题，请查看：
1. 项目日志文件 `wdt_enterprise_api.log`
2. 旺店通开放平台文档
3. 提交Issue到项目仓库
