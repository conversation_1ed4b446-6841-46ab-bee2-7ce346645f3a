#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取当天销售订单并导出到Excel（简化版，不依赖pandas）
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from wdt_post_client import WDTPostClient

def export_daily_orders_simple():
    """简化版当天订单导出"""
    
    print("🎯 旺店通销售订单导出工具（简化版）")
    print("=" * 50)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 获取今天的时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    # 如果是今天，结束时间不能超过当前时间
    current_time = datetime.now()
    if end_time > current_time:
        end_time = current_time
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    all_orders = []
    
    # 查询不同状态的订单
    status_map = {
        30: "待审核",
        5: "已取消"
    }
    
    for status_code, status_name in status_map.items():
        print(f"\n🔍 查询{status_name}订单...")
        
        try:
            page_no = 0
            page_size = 100  # 最大页面大小
            
            while True:
                response = client.query_sales_trades(
                    start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    trade_status=status_code,
                    page_no=page_no,
                    page_size=page_size
                )
                
                total = response.get('total', 0)
                trades = response.get('content', [])
                
                print(f"   📊 第{page_no + 1}页: {len(trades)} 条订单")
                
                if not trades:
                    break
                
                # 添加状态名称到每个订单
                for trade in trades:
                    trade['status_name'] = status_name
                
                all_orders.extend(trades)
                
                # 如果当前页数据少于页面大小，说明已经是最后一页
                if len(trades) < page_size:
                    break
                
                page_no += 1
            
            print(f"   ✅ {status_name}订单总计: {len([o for o in all_orders if o.get('status_name') == status_name])} 条")
            
        except Exception as e:
            print(f"   ❌ 查询{status_name}订单失败: {e}")
    
    print(f"\n📈 总计获取订单: {len(all_orders)} 条")
    
    if not all_orders:
        print("\n📝 当天没有订单数据")
        return
    
    # 导出到Excel
    filename = f"销售订单_{today.strftime('%Y%m%d')}.xlsx"
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 创建订单明细表
        ws_detail = wb.active
        ws_detail.title = "销售订单明细"
        
        # 定义表头
        headers = [
            'ERP单号', '原始单号', '仓储单号', '订单状态', '状态代码',
            '货主编号', '仓库编号', '店铺名称', '店铺编号', '物流编码',
            '买家昵称', '收件人省', '收件人市', '收件人区', '收件人地址',
            '订单总金额', '货品数量', '货品种类数', '预估重量', '订单体积',
            '客服备注', '买家留言', '平台下单时间', '付款时间', '接单时间',
            '平台名称', '订单类型', '发货条件', 'COD金额', '标记名称',
            '商品序号', '商品编码', '商品数量', '商品重量', '商品体积',
            '商品备注', '行号'
        ]
        
        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws_detail.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 写入数据
        row = 2
        for order in all_orders:
            # 基础订单信息
            base_data = [
                order.get('trade_no', ''),
                order.get('src_tids', ''),
                order.get('src_order_no', ''),
                order.get('status_name', ''),
                order.get('trade_status', ''),
                order.get('owner_no', ''),
                order.get('warehouse_no', ''),
                order.get('shop_name', ''),
                order.get('shop_no', ''),
                order.get('logistics_code', ''),
                order.get('buyer_nick', ''),
                order.get('receiver_province', ''),
                order.get('receiver_city', ''),
                order.get('receiver_district', ''),
                order.get('receiver_area', ''),
                order.get('total_amount', ''),
                order.get('goods_count', ''),
                order.get('goods_type_count', ''),
                order.get('weight', ''),
                order.get('volume', ''),
                order.get('cs_remark', ''),
                order.get('buyer_message', ''),
                order.get('trade_time', ''),
                order.get('pay_time', ''),
                order.get('trade_create_time', ''),
                order.get('platform_name', ''),
                get_trade_type_name(order.get('trade_type', 0)),
                get_delivery_term_name(order.get('delivery_term', 1)),
                order.get('cod_amount', ''),
                order.get('flag_name', '')
            ]
            
            # 商品明细
            goods_detail = order.get('goods_detail', [])
            if goods_detail:
                for i, goods in enumerate(goods_detail):
                    goods_data = [
                        i + 1,  # 商品序号
                        goods.get('spec_no', ''),
                        goods.get('num', ''),
                        goods.get('weight', ''),
                        goods.get('volume', ''),
                        goods.get('remark', ''),
                        goods.get('orderline_no', '')
                    ]
                    
                    # 写入完整行数据
                    full_row_data = base_data + goods_data
                    for col, value in enumerate(full_row_data, 1):
                        ws_detail.cell(row=row, column=col, value=value)
                    row += 1
            else:
                # 没有商品明细的订单，商品相关字段留空
                goods_data = ['', '', '', '', '', '', '']
                full_row_data = base_data + goods_data
                for col, value in enumerate(full_row_data, 1):
                    ws_detail.cell(row=row, column=col, value=value)
                row += 1
        
        # 自动调整列宽
        for column in ws_detail.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_detail.column_dimensions[column_letter].width = adjusted_width
        
        # 创建汇总表
        ws_summary = wb.create_sheet("订单汇总")
        
        # 汇总表头
        summary_headers = ['订单状态', '订单数量', '总金额', '总货品数']
        for col, header in enumerate(summary_headers, 1):
            cell = ws_summary.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 计算汇总数据
        status_summary = {}
        total_amount = 0
        total_goods_count = 0
        
        for order in all_orders:
            status = order.get('status_name', '未知')
            if status not in status_summary:
                status_summary[status] = {
                    '订单数量': 0,
                    '总金额': 0,
                    '总货品数': 0
                }
            
            status_summary[status]['订单数量'] += 1
            
            # 处理金额
            amount = order.get('total_amount', 0)
            if isinstance(amount, str):
                try:
                    amount = float(amount)
                except:
                    amount = 0
            status_summary[status]['总金额'] += amount
            total_amount += amount
            
            # 处理货品数量
            goods_count = order.get('goods_count', 0)
            if isinstance(goods_count, str):
                try:
                    goods_count = float(goods_count)
                except:
                    goods_count = 0
            status_summary[status]['总货品数'] += goods_count
            total_goods_count += goods_count
        
        # 写入汇总数据
        summary_row = 2
        for status, data in status_summary.items():
            ws_summary.cell(row=summary_row, column=1, value=status)
            ws_summary.cell(row=summary_row, column=2, value=data['订单数量'])
            ws_summary.cell(row=summary_row, column=3, value=round(data['总金额'], 2))
            ws_summary.cell(row=summary_row, column=4, value=round(data['总货品数'], 2))
            summary_row += 1
        
        # 添加总计行
        ws_summary.cell(row=summary_row, column=1, value='总计')
        ws_summary.cell(row=summary_row, column=2, value=len(all_orders))
        ws_summary.cell(row=summary_row, column=3, value=round(total_amount, 2))
        ws_summary.cell(row=summary_row, column=4, value=round(total_goods_count, 2))
        
        # 保存文件
        wb.save(filename)
        
        print(f"✅ 导出成功！")
        print(f"📊 总计导出: {len(all_orders)} 条订单")
        print(f"📁 文件路径: {filename}")
        
        # 显示汇总信息
        print(f"\n📊 订单汇总:")
        for status, data in status_summary.items():
            print(f"   {status}: {data['订单数量']} 条，总金额: {data['总金额']:.2f}")
        
        print(f"\n🎉 导出完成！")
        
    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

def get_trade_type_name(trade_type: int) -> str:
    """获取订单类型名称"""
    type_map = {
        0: "正常订单",
        1: "换货订单", 
        2: "补发订单"
    }
    return type_map.get(trade_type, f"未知类型({trade_type})")

def get_delivery_term_name(delivery_term: int) -> str:
    """获取发货条件名称"""
    term_map = {
        1: "非货到付款",
        2: "货到付款"
    }
    return term_map.get(delivery_term, f"未知条件({delivery_term})")

if __name__ == "__main__":
    export_daily_orders_simple()
