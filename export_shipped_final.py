#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用正确参数查询今天已发货的订单（出库单）
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from wdt_post_client import WDTPostClient

def export_shipped_final():
    """使用正确参数查询今天已发货的订单"""
    
    print("🚚 查询今天已发货的订单（出库单）")
    print("=" * 50)
    
    print("💡 说明:")
    print("   sales.trade.query 接口只支持状态 5(已取消) 和 30(待审核)")
    print("   已发货的订单需要通过 stockout.query 接口查询")
    print("   stockout.query 需要特定的查询参数组合")
    print()
    
    # 创建客户端
    client = WDTPostClient()
    
    # 获取今天的时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    # 如果是今天，结束时间不能超过当前时间
    current_time = datetime.now()
    if end_time > current_time:
        end_time = current_time
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 尝试不同的参数组合
    print(f"\n🔍 尝试不同的参数组合查询出库单...")
    
    param_combinations = [
        {
            'name': '使用发货时间范围',
            'params': {
                "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                "page_no": 0,
                "page_size": 10
            }
        },
        {
            'name': '使用货主编号',
            'params': {
                "owner_no": "changhe",  # 使用我们的SID作为货主编号
                "page_no": 0,
                "page_size": 10
            }
        },
        {
            'name': '使用货主编号 + 发货时间',
            'params': {
                "owner_no": "changhe",
                "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                "page_no": 0,
                "page_size": 10
            }
        },
        {
            'name': '使用空的货主编号',
            'params': {
                "owner_no": "",
                "page_no": 0,
                "page_size": 10
            }
        }
    ]
    
    successful_params = None
    stockout_data = []
    
    for combination in param_combinations:
        print(f"\n🧪 测试参数组合: {combination['name']}")
        print(f"📝 参数: {json.dumps(combination['params'], ensure_ascii=False, indent=2)}")
        
        try:
            response = client.call_api('stockout.query', combination['params'])
            
            print(f"✅ 参数组合 '{combination['name']}' 调用成功！")
            print(f"📊 响应: {json.dumps(response, ensure_ascii=False, indent=2)}")
            
            successful_params = combination
            
            # 检查响应数据
            content = response.get('content', [])
            if isinstance(content, list) and content:
                stockout_data = content
                print(f"📈 找到 {len(content)} 条出库单数据")
            elif isinstance(content, dict):
                stockout_data = content.get('content', []) or content.get('stockouts', [])
                total = content.get('total', len(stockout_data))
                print(f"📈 找到 {total} 条出库单数据")
            else:
                print(f"📝 当前时间范围内无出库单数据")
            
            break
            
        except Exception as e:
            error_msg = str(e)
            print(f"❌ 参数组合 '{combination['name']}' 失败: {error_msg}")
    
    if successful_params:
        print(f"\n🎉 成功找到可用的参数组合: {successful_params['name']}")
        
        # 获取完整的出库单数据
        print(f"\n📊 获取完整出库单数据...")
        
        all_stockouts = []
        page_no = 0
        page_size = 100
        
        while True:
            try:
                # 更新分页参数
                current_params = successful_params['params'].copy()
                current_params['page_no'] = page_no
                current_params['page_size'] = page_size
                
                response = client.call_api('stockout.query', current_params)
                
                content = response.get('content', [])
                if isinstance(content, dict):
                    stockouts = content.get('content', []) or content.get('stockouts', [])
                    total = content.get('total', 0)
                else:
                    stockouts = content if isinstance(content, list) else []
                    total = len(stockouts)
                
                print(f"   📊 第{page_no + 1}页: {len(stockouts)} 条出库单")
                
                if not stockouts:
                    break
                
                all_stockouts.extend(stockouts)
                
                if len(stockouts) < page_size:
                    break
                
                page_no += 1
                
            except Exception as e:
                print(f"   ❌ 获取第{page_no + 1}页失败: {e}")
                break
        
        print(f"\n📈 总计获取出库单: {len(all_stockouts)} 条")
        
        if all_stockouts:
            # 导出到Excel
            filename = f"已发货订单_出库单_{today.strftime('%Y%m%d')}.xlsx"
            
            try:
                from openpyxl import Workbook
                from openpyxl.styles import Font, PatternFill, Alignment
                
                print(f"\n📊 开始导出到Excel...")
                print(f"📁 文件名: {filename}")
                
                # 创建工作簿
                wb = Workbook()
                
                # 创建出库单明细表
                ws_detail = wb.active
                ws_detail.title = "出库单明细"
                
                # 分析第一条数据的结构
                sample_stockout = all_stockouts[0]
                print(f"\n📋 出库单数据结构示例:")
                for key, value in sample_stockout.items():
                    if isinstance(value, (dict, list)):
                        print(f"   {key}: {type(value).__name__}")
                    else:
                        print(f"   {key}: {value}")
                
                # 定义表头（基于常见的出库单字段）
                headers = []
                for key in sample_stockout.keys():
                    headers.append(key)
                
                print(f"\n📋 表头字段: {headers}")
                
                # 写入表头
                for col, header in enumerate(headers, 1):
                    cell = ws_detail.cell(row=1, column=col, value=header)
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")
                
                # 写入数据
                for row_idx, stockout in enumerate(all_stockouts, 2):
                    for col_idx, header in enumerate(headers, 1):
                        value = stockout.get(header, '')
                        
                        # 处理复杂数据类型
                        if isinstance(value, (dict, list)):
                            value = json.dumps(value, ensure_ascii=False)
                        
                        ws_detail.cell(row=row_idx, column=col_idx, value=value)
                
                # 自动调整列宽
                for column in ws_detail.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    ws_detail.column_dimensions[column_letter].width = adjusted_width
                
                # 创建汇总表
                ws_summary = wb.create_sheet("出库单汇总")
                
                # 汇总表头
                summary_headers = ['统计项', '数量/信息']
                for col, header in enumerate(summary_headers, 1):
                    cell = ws_summary.cell(row=1, column=col, value=header)
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")
                
                # 写入汇总数据
                summary_data = [
                    ('出库单总数', len(all_stockouts)),
                    ('查询日期', today.strftime('%Y-%m-%d')),
                    ('导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                    ('查询接口', 'stockout.query'),
                    ('参数组合', successful_params['name']),
                    ('时间范围', f"{start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")
                ]
                
                for row_idx, (item, value) in enumerate(summary_data, 2):
                    ws_summary.cell(row=row_idx, column=1, value=item)
                    ws_summary.cell(row=row_idx, column=2, value=str(value))
                
                # 保存文件
                wb.save(filename)
                
                print(f"✅ 导出成功！")
                print(f"📊 总计导出: {len(all_stockouts)} 条出库单")
                print(f"📁 文件路径: {filename}")
                
                # 显示部分数据样例
                print(f"\n📋 出库单数据样例:")
                for i, stockout in enumerate(all_stockouts[:3], 1):
                    print(f"   出库单 {i}:")
                    for key, value in list(stockout.items())[:5]:  # 只显示前5个字段
                        if not isinstance(value, (dict, list)):
                            print(f"     {key}: {value}")
                
                print(f"\n🎉 已发货订单（出库单）导出完成！")
                
            except ImportError:
                print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
            except Exception as e:
                print(f"❌ 导出失败: {e}")
        else:
            print("📝 今天没有出库单数据")
    else:
        print(f"\n❌ 所有参数组合都失败了")
        print(f"💡 建议:")
        print(f"   1. 联系旺店通技术支持确认 stockout.query 接口的正确参数")
        print(f"   2. 确认货主编号是否正确")
        print(f"   3. 确认是否有出库单查询权限")
        print(f"   4. 检查今天是否有实际的出库单数据")

if __name__ == "__main__":
    export_shipped_final()
