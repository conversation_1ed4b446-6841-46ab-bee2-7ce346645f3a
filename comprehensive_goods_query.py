#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合货品信息查询方案
使用 goods.spec.query.step 增量接口获取货品编号和备注
通过扩大时间范围来获取更多商品的档案信息
"""

import json
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient

class ComprehensiveGoodsQuery:
    """综合货品信息查询器"""
    
    def __init__(self):
        self.client = WDTPostClient()
        self.owner_no = "AJT-XLSWDT"
        self.goods_archive = {}  # 货品档案缓存
        
    def query_goods_archive_by_time_range(self, days_back=30):
        """通过扩大时间范围查询货品档案信息"""
        
        print(f"🔍 查询最近 {days_back} 天的货品档案变更信息...")
        
        now = datetime.now()
        total_goods_found = 0
        
        # 按天查询，每次查询1小时的时间窗口
        for day in range(days_back):
            query_date = now - timedelta(days=day)
            
            # 每天查询多个时间段
            time_slots = [
                (0, 1),   # 00:00-01:00
                (6, 7),   # 06:00-07:00
                (9, 10),  # 09:00-10:00
                (12, 13), # 12:00-13:00
                (15, 16), # 15:00-16:00
                (18, 19), # 18:00-19:00
                (21, 22), # 21:00-22:00
            ]
            
            day_found = 0
            
            for start_hour, end_hour in time_slots:
                start_time = query_date.replace(hour=start_hour, minute=0, second=0, microsecond=0)
                end_time = query_date.replace(hour=end_hour, minute=0, second=0, microsecond=0)
                
                try:
                    response = self.client.call_api('goods.spec.query.step', {
                        "owner_no": self.owner_no,
                        "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "page_size": 100,
                        "page_no": 0
                    })
                    
                    if response and 'content' in response:
                        content = response.get('content', [])
                        if isinstance(content, list) and content:
                            slot_found = 0
                            for item in content:
                                spec_no = item.get('spec_no', '')
                                if spec_no and spec_no not in self.goods_archive:
                                    self.goods_archive[spec_no] = {
                                        'goods_no': item.get('goods_no', ''),
                                        'goods_name': item.get('goods_name', ''),
                                        'spec_name': item.get('spec_name', ''),
                                        'remark': item.get('remark', ''),
                                        'description': item.get('description', ''),
                                        'memo': item.get('memo', ''),
                                        'note': item.get('note', ''),
                                        'barcode': item.get('barcode', ''),
                                        'brand_name': item.get('brand_name', ''),
                                        'update_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                                        'full_data': item
                                    }
                                    slot_found += 1
                            
                            if slot_found > 0:
                                day_found += slot_found
                                print(f"  📅 {query_date.strftime('%Y-%m-%d')} {start_hour:02d}:00-{end_hour:02d}:00: 找到 {slot_found} 个商品")
                
                except Exception as e:
                    # 忽略错误，继续查询其他时间段
                    pass
            
            if day_found > 0:
                total_goods_found += day_found
                print(f"✅ {query_date.strftime('%Y-%m-%d')}: 总计找到 {day_found} 个商品")
        
        print(f"\n📊 查询完成！总计找到 {total_goods_found} 个商品的档案信息")
        print(f"📋 货品档案缓存大小: {len(self.goods_archive)}")
        
        return self.goods_archive
    
    def get_goods_info(self, spec_no):
        """获取指定商品的档案信息"""
        return self.goods_archive.get(spec_no, {
            'goods_no': '',
            'remark': '',
            'description': '',
            'memo': '',
            'note': '',
            'full_data': None
        })
    
    def save_goods_archive(self, filename=None):
        """保存货品档案到文件"""
        if not filename:
            filename = f"goods_archive_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.goods_archive, f, ensure_ascii=False, indent=2)
        
        print(f"📁 货品档案已保存到: {filename}")
        return filename
    
    def load_goods_archive(self, filename):
        """从文件加载货品档案"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.goods_archive = json.load(f)
            print(f"📁 成功加载货品档案: {len(self.goods_archive)} 个商品")
            return True
        except Exception as e:
            print(f"❌ 加载货品档案失败: {e}")
            return False
    
    def analyze_goods_archive(self):
        """分析货品档案数据"""
        if not self.goods_archive:
            print("❌ 货品档案为空")
            return
        
        total_count = len(self.goods_archive)
        goods_no_count = sum(1 for item in self.goods_archive.values() if item.get('goods_no'))
        remark_count = sum(1 for item in self.goods_archive.values() if item.get('remark'))
        
        print(f"\n📊 货品档案分析:")
        print(f"📦 总商品数: {total_count}")
        print(f"🔢 有货品编号的商品: {goods_no_count} ({goods_no_count/total_count*100:.1f}%)")
        print(f"📝 有备注的商品: {remark_count} ({remark_count/total_count*100:.1f}%)")
        
        # 显示一些示例
        print(f"\n📋 示例数据:")
        count = 0
        for spec_no, info in self.goods_archive.items():
            if count >= 5:
                break
            print(f"  {spec_no}:")
            print(f"    货品编号: {info.get('goods_no', '无')}")
            print(f"    商品名称: {info.get('goods_name', '无')}")
            print(f"    备注: {info.get('remark', '无')}")
            count += 1

def main():
    """主函数"""
    print("🔍 综合货品信息查询")
    print("=" * 60)
    
    # 创建查询器
    query = ComprehensiveGoodsQuery()
    
    # 查询货品档案（查询最近30天的变更）
    print("📦 开始查询货品档案信息...")
    goods_archive = query.query_goods_archive_by_time_range(days_back=30)
    
    # 分析结果
    query.analyze_goods_archive()
    
    # 保存结果
    archive_file = query.save_goods_archive()
    
    print(f"\n💡 使用建议:")
    print(f"1. 货品档案文件已保存为: {archive_file}")
    print(f"2. 可以在导出脚本中加载这个文件来获取货品编号和备注")
    print(f"3. 建议定期运行此脚本更新货品档案")
    print(f"4. 如果需要更多商品信息，可以增加 days_back 参数")
    
    return query

if __name__ == "__main__":
    main()
