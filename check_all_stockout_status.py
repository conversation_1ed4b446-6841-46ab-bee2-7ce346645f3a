#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查当日所有出库单状态分布
用于了解当天所有出库单的状态分布情况
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def check_all_stockout_status():
    """检查当日所有出库单状态分布"""
    
    print("🔍 检查当日所有出库单状态分布")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 时间范围: {start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")
    
    # 查询参数 - 不限制状态，获取所有出库单
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 100
    }
    
    print(f"🔍 开始查询所有出库单...")
    
    all_stockouts = []
    status_stats = {}
    page_no = 0
    max_pages = 50  # 最多查询50页
    
    while page_no < max_pages:
        print(f"\n📄 正在获取第 {page_no + 1} 页数据...")
        
        try:
            # 当前页参数
            current_params = {
                **base_params,
                "page_no": page_no
            }
            
            # 调用API
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                print(f"❌ 第 {page_no + 1} 页响应为空")
                break
            
            # 获取数据
            content = response.get('content', [])
            total = response.get('total', 0)
            
            print(f"📊 API返回: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
            
            # 处理数据
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
                if not isinstance(page_data, list):
                    page_data = [page_data] if page_data else []
            
            if not page_data:
                print(f"📝 第 {page_no + 1} 页无数据，停止查询")
                break
            
            # 统计状态分布
            for item in page_data:
                if isinstance(item, dict):
                    status = str(item.get('status', ''))
                    status_name = get_status_name(status)
                    status_stats[f"{status}({status_name})"] = status_stats.get(f"{status}({status_name})", 0) + 1
                    all_stockouts.append(item)
            
            print(f"✅ 第 {page_no + 1} 页: 获取 {len(page_data)} 条数据")
            print(f"📈 累计出库单: {len(all_stockouts)} 条")
            
            # 如果当前页数据少于页面大小，说明已经是最后一页
            if len(page_data) < base_params["page_size"]:
                print(f"🏁 已获取完所有数据 (当前页数据:{len(page_data)}, 页面大小:{base_params['page_size']})")
                break
            
            page_no += 1
                
        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            page_no += 1
            continue
    
    print(f"\n📊 查询完成！总计获取 {len(all_stockouts)} 条出库单")
    
    # 显示状态分布
    if status_stats:
        print(f"\n📋 出库单状态分布:")
        for status, count in sorted(status_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"   {status}: {count} 单")
    
    # 统计已发货的数量
    shipped_count = 0
    for stockout in all_stockouts:
        if str(stockout.get('status', '')) == '95':
            shipped_count += 1
    
    print(f"\n🚛 已发货订单统计:")
    print(f"   已发货(95): {shipped_count} 单")
    print(f"   占比: {shipped_count/len(all_stockouts)*100:.1f}%" if all_stockouts else "0%")
    
    return all_stockouts, status_stats

def get_status_name(status):
    """获取状态名称"""
    status_map = {
        '5': '已取消',
        '10': '待放回',
        '50': '待审核',
        '51': '缺货',
        '52': '缺货待入库',
        '53': 'WMS已接单',
        '54': '获取电子面单',
        '58': '档口锁定',
        '60': '待分配',
        '61': '排队中',
        '63': '待补货',
        '65': '待处理',
        '70': '待发货',
        '73': '爆款锁定',
        '75': '待拣货',
        '77': '拣货中',
        '79': '已拣货',
        '90': '延时发货',
        '95': '已发货',
        '110': '已完成',
        '-1': '未发货'
    }
    return status_map.get(status, f'未知状态')

if __name__ == "__main__":
    check_all_stockout_status()
