#!/usr/bin/env python3
"""
旺店通WMS销售出库单查询主程序
"""
import argparse
import logging
from datetime import datetime, timedelta, date
import sys
import os

from wdt_client import WDTClient, WDTAPIException
from sales_stockout import SalesStockoutQuery
from data_processor import SalesStockoutProcessor
from config import WDTConfig


def setup_logging(log_level='INFO'):
    """设置日志"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('wdt_api.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )


def parse_date(date_str):
    """解析日期字符串"""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        try:
            return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            raise argparse.ArgumentTypeError(f"无效的日期格式: {date_str}，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='旺店通WMS销售出库单查询工具')
    
    # 时间参数
    parser.add_argument('--start-date', type=parse_date, 
                       help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=parse_date,
                       help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--hours', type=int, default=1,
                       help='查询最近N小时的数据 (默认: 1)')
    parser.add_argument('--days', type=int,
                       help='查询最近N天的数据')
    
    # 查询条件
    parser.add_argument('--status-type', type=int, default=0, choices=[0, 1, 2, 3],
                       help='出库单状态类型 (0:延时发货&已完成, 1:已取消, 2:待分配~延时发货, 3:按指定status查询)')
    parser.add_argument('--status', type=str,
                       help='出库单状态详细 (当status-type=3时使用)')
    parser.add_argument('--warehouse-no', type=str,
                       help='仓库编号')
    parser.add_argument('--shop-nos', type=str,
                       help='店铺编号 (多个用逗号分隔)')
    parser.add_argument('--stockout-no', type=str,
                       help='出库单号')
    parser.add_argument('--order-no', type=str,
                       help='系统订单号')
    parser.add_argument('--logistics-no', type=str,
                       help='物流单号')
    
    # 输出选项
    parser.add_argument('--output-format', choices=['excel', 'csv', 'json', 'console'], 
                       default='console', help='输出格式 (默认: console)')
    parser.add_argument('--output-file', type=str,
                       help='输出文件名 (不指定则自动生成)')
    parser.add_argument('--page-size', type=int, default=100,
                       help='分页大小 (默认: 100)')
    parser.add_argument('--max-pages', type=int,
                       help='最大页数限制')
    
    # 其他选项
    parser.add_argument('--need-sn', action='store_true',
                       help='返回SN信息')
    parser.add_argument('--get-anchor', action='store_true',
                       help='获取主播信息')
    parser.add_argument('--stats', action='store_true',
                       help='显示统计信息')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    try:
        # 验证配置
        WDTConfig.validate_config()
        logger.info("配置验证通过")
        
        # 确定查询时间范围
        if args.start_date and args.end_date:
            start_time = args.start_date
            end_time = args.end_date
        elif args.days:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=args.days)
        else:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=args.hours)
        
        logger.info(f"查询时间范围: {start_time} - {end_time}")
        
        # 创建查询器
        query = SalesStockoutQuery()
        
        # 构建查询参数
        query_params = {
            'status_type': args.status_type,
            'need_sn': args.need_sn,
            'get_anchor': args.get_anchor
        }
        
        # 添加可选参数
        if args.status:
            query_params['status'] = args.status
        if args.warehouse_no:
            query_params['warehouse_no'] = args.warehouse_no
        if args.shop_nos:
            query_params['shop_nos'] = args.shop_nos
        if args.stockout_no:
            query_params['stockout_no'] = args.stockout_no
        if args.order_no:
            query_params['src_order_no'] = args.order_no
        if args.logistics_no:
            query_params['logistics_no'] = args.logistics_no
        
        # 执行查询
        logger.info("开始查询销售出库单...")
        
        if isinstance(start_time, date) and isinstance(end_time, date):
            # 日期范围查询
            orders = query.query_sales_stockout_by_date_range(
                start_date=start_time,
                end_date=end_time,
                **query_params
            )
        else:
            # 时间范围查询
            orders = query.query_sales_stockout_all_pages(
                start_time=start_time,
                end_time=end_time,
                page_size=args.page_size,
                max_pages=args.max_pages,
                **query_params
            )
        
        logger.info(f"查询完成，共获取 {len(orders)} 条记录")
        
        if not orders:
            print("没有查询到符合条件的数据")
            return
        
        # 处理输出
        processor = SalesStockoutProcessor()
        
        if args.output_format == 'console':
            # 控制台输出
            print(f"\n查询结果: 共 {len(orders)} 条记录")
            print("-" * 80)
            
            for i, order in enumerate(orders[:10], 1):  # 只显示前10条
                print(f"{i:2d}. 出库单号: {order.get('order_no')}")
                print(f"    订单号: {order.get('trade_no')}")
                print(f"    状态: {query.get_stockout_status_name(order.get('status'))}")
                print(f"    发货时间: {order.get('consign_time')}")
                print(f"    收件人: {order.get('receiver_name')}")
                print(f"    货品数量: {order.get('goods_count')}")
                print()
            
            if len(orders) > 10:
                print(f"... 还有 {len(orders) - 10} 条记录")
        
        elif args.output_format == 'excel':
            filepath = processor.export_to_excel(orders, args.output_file)
            print(f"数据已导出到Excel文件: {filepath}")
        
        elif args.output_format == 'csv':
            filepath = processor.export_to_csv(orders, args.output_file)
            print(f"数据已导出到CSV文件: {filepath}")
        
        elif args.output_format == 'json':
            filepath = processor.export_to_json(orders, args.output_file)
            print(f"数据已导出到JSON文件: {filepath}")
        
        # 显示统计信息
        if args.stats:
            stats = processor.get_summary_statistics(orders)
            print("\n=== 统计信息 ===")
            for key, value in stats.items():
                if isinstance(value, dict):
                    print(f"{key}:")
                    for k, v in value.items():
                        print(f"  {k}: {v}")
                else:
                    print(f"{key}: {value}")
        
    except WDTAPIException as e:
        logger.error(f"API调用失败: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        logger.exception("详细错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
