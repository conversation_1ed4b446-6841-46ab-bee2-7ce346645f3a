#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎉 旺店通WMS API 成功使用示例
"""

import json
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient

def success_example():
    """成功使用示例"""
    
    print("🎉 旺店通WMS API 成功使用示例")
    print("="*50)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 示例1: 查询最近1小时的待审核订单
    print("\n📋 示例1: 查询最近1小时的待审核订单")
    print("-" * 40)
    
    try:
        now = datetime.now()
        start_time = now - timedelta(hours=1)
        
        response = client.query_sales_trades(
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=now.strftime('%Y-%m-%d %H:%M:%S'),
            trade_status=30,  # 待审核
            page_no=0,
            page_size=10
        )
        
        print(f"✅ 查询成功！")
        print(f"📊 总计: {response.get('total', 0)} 条订单")
        
        trades = response.get('content', [])
        if trades:
            print(f"📋 订单列表:")
            for i, trade in enumerate(trades[:3], 1):
                print(f"  {i}. {trade.get('trade_no')} - {trade.get('trade_status')}")
        else:
            print("📝 当前时间段无订单数据")
            
    except Exception as e:
        print(f"❌ 查询失败: {e}")
    
    # 示例2: 查询昨天的已取消订单
    print("\n📋 示例2: 查询昨天的已取消订单")
    print("-" * 40)
    
    try:
        yesterday = datetime.now() - timedelta(days=1)
        start_time = yesterday.replace(hour=0, minute=0, second=0)
        end_time = yesterday.replace(hour=23, minute=59, second=59)
        
        response = client.query_sales_trades(
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
            trade_status=5,  # 已取消
            page_no=0,
            page_size=5
        )
        
        print(f"✅ 查询成功！")
        print(f"📊 昨天已取消订单: {response.get('total', 0)} 条")
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
    
    # 示例3: 分页查询
    print("\n📋 示例3: 分页查询示例")
    print("-" * 40)
    
    try:
        now = datetime.now()
        start_time = now - timedelta(hours=6)
        
        # 第一页
        page1 = client.query_sales_trades(
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=now.strftime('%Y-%m-%d %H:%M:%S'),
            trade_status=30,
            page_no=0,
            page_size=5
        )
        
        print(f"✅ 第一页查询成功！")
        print(f"📊 总计: {page1.get('total', 0)} 条，当前页: {len(page1.get('content', []))} 条")
        
        # 如果有更多数据，查询第二页
        if page1.get('total', 0) > 5:
            page2 = client.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=now.strftime('%Y-%m-%d %H:%M:%S'),
                trade_status=30,
                page_no=1,
                page_size=5
            )
            print(f"✅ 第二页查询成功！当前页: {len(page2.get('content', []))} 条")
        
    except Exception as e:
        print(f"❌ 分页查询失败: {e}")
    
    # 示例4: 带可选参数的查询
    print("\n📋 示例4: 带可选参数的查询")
    print("-" * 40)
    
    try:
        now = datetime.now()
        start_time = now - timedelta(hours=2)
        
        response = client.query_sales_trades(
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=now.strftime('%Y-%m-%d %H:%M:%S'),
            trade_status=30,
            page_no=0,
            page_size=10,
            owner_no="changhe",  # 指定货主
            is_exist_flag=0      # 全部数据
        )
        
        print(f"✅ 带参数查询成功！")
        print(f"📊 指定货主的订单: {response.get('total', 0)} 条")
        
    except Exception as e:
        print(f"❌ 带参数查询失败: {e}")
    
    print("\n" + "="*50)
    print("🎊 恭喜！旺店通WMS API集成完全成功！")
    print("="*50)
    
    print("\n🚀 您现在可以:")
    print("  ✅ 查询销售订单数据")
    print("  ✅ 支持分页查询")
    print("  ✅ 支持状态筛选")
    print("  ✅ 支持时间范围查询")
    print("  ✅ 支持可选参数")
    print("  ✅ 完整的错误处理")
    print("  ✅ 响应数据解压缩")
    
    print("\n📚 技术特点:")
    print("  🔧 POST请求方法")
    print("  📄 JSON请求体")
    print("  🔐 MD5签名认证")
    print("  🗜️ 响应压缩支持")
    print("  ⚡ 完整的异常处理")
    print("  📊 详细的日志记录")
    
    print("\n🎯 下一步建议:")
    print("  1. 🏭 投入生产环境使用")
    print("  2. 📈 开发更多业务功能")
    print("  3. 🔄 集成到现有系统")
    print("  4. 📊 添加数据分析功能")
    print("  5. 🚨 完善监控和告警")

if __name__ == "__main__":
    success_example()
