#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找正确的货品编号字段
分析API返回的完整数据结构，找到货品编号的正确字段名
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def find_goods_no_field():
    """查找正确的货品编号字段"""
    
    print("🔍 查找正确的货品编号字段")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    
    # 查询参数
    params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 5,  # 只取5条数据用于分析
        "page_no": 0
    }
    
    print(f"🔍 获取样本数据进行详细字段分析...")
    
    try:
        response = client.call_api('stockout.query', params)
        
        if not response:
            print("❌ API响应为空")
            return
        
        content = response.get('content', [])
        total = response.get('total', 0)
        
        print(f"📊 API返回: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
        
        # 处理数据
        page_data = []
        if isinstance(content, list):
            page_data = content
        elif isinstance(content, dict):
            page_data = content.get('content', [])
            if not isinstance(page_data, list):
                page_data = [page_data] if page_data else []
        
        if not page_data:
            print("📝 无数据")
            return
        
        print(f"\n📋 详细分析前 {len(page_data)} 条数据的字段结构...")
        
        # 分析每条数据的完整结构
        for i, item in enumerate(page_data[:3]):  # 只分析前3条
            print(f"\n🔍 第 {i+1} 条出库单详细分析:")
            print(f"出库单号: {item.get('stockout_no', 'N/A')}")
            print(f"状态: {item.get('status', 'N/A')}")
            
            # 分析出库单级别的所有字段
            print(f"\n📦 出库单级别的所有字段:")
            for key, value in item.items():
                if key != 'goods_detail':  # 商品明细单独分析
                    print(f"  {key}: {value}")
            
            # 重点分析商品明细字段
            goods_detail = item.get('goods_detail', [])
            print(f"\n🛍️ 商品明细字段分析:")
            print(f"goods_detail类型: {type(goods_detail)}")
            print(f"goods_detail长度: {len(goods_detail) if isinstance(goods_detail, list) else 'N/A'}")
            
            if isinstance(goods_detail, list) and goods_detail:
                for j, detail in enumerate(goods_detail):
                    print(f"\n  📦 第 {j+1} 个商品的所有字段:")
                    for key, value in detail.items():
                        print(f"    {key}: {value}")
                    
                    # 特别检查可能的货品编号字段
                    possible_goods_fields = [
                        'goods_no', 'goods_code', 'goods_id', 'product_no', 'product_code', 'product_id',
                        'item_no', 'item_code', 'item_id', 'sku_no', 'sku_code', 'sku_id',
                        'spec_no', 'spec_code', 'spec_id', 'barcode', 'goods_name'
                    ]
                    
                    print(f"\n    🔍 可能的货品相关字段:")
                    for field in possible_goods_fields:
                        if field in detail:
                            print(f"      ✅ {field}: {detail.get(field, 'N/A')}")
                    
                    # 检查是否有其他可能的字段
                    print(f"\n    🔍 所有字段名列表:")
                    print(f"      {list(detail.keys())}")
            
            print(f"\n" + "="*50)
        
        # 保存完整的样本数据到文件进行进一步分析
        sample_filename = f"complete_sample_data_{today.strftime('%Y%m%d')}.json"
        with open(sample_filename, 'w', encoding='utf-8') as f:
            json.dump({
                'total': total,
                'sample_count': len(page_data),
                'complete_data': page_data[:3]  # 保存前3条完整数据
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 完整样本数据已保存到: {sample_filename}")
        print(f"📝 请查看该文件以获取完整的字段结构信息")
        
        # 尝试查找货品编号的线索
        print(f"\n🔍 货品编号字段分析总结:")
        print(f"根据WMS系统的常见字段命名规则，货品编号可能的字段名包括:")
        print(f"1. goods_no - 货品编号（最常见）")
        print(f"2. goods_code - 货品代码")
        print(f"3. product_no - 产品编号")
        print(f"4. item_no - 商品编号")
        print(f"5. sku_no - SKU编号")
        print(f"6. barcode - 条码（有时也作为货品标识）")
        print(f"\n从上面的分析中，我们需要确定哪个字段是真正的货品编号。")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    find_goods_no_field()
