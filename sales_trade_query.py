#!/usr/bin/env python3
"""
旺店通WMS销售订单查询模块
基于 sales.trade.query 接口
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from wdt_sales_trade_client import WDTSalesTradeClient, WDTSalesTradeAPIException
from config import WDTConfig


class SalesTradeQuery:
    """销售订单查询类"""
    
    def __init__(self, config: Optional[WDTConfig] = None):
        """
        初始化查询器
        
        Args:
            config: 配置类，如果为None则使用默认配置
        """
        self.client = WDTSalesTradeClient(config)
        self.logger = logging.getLogger(__name__)
    
    def query_trades(
        self,
        start_time: datetime,
        end_time: datetime,
        trade_status: int = 30,
        owner_no: Optional[str] = None,
        warehouse_no: Optional[str] = None,
        is_exist_flag: Optional[int] = None,
        page_size: int = 100,
        page_no: int = 0
    ) -> Dict[str, Any]:
        """
        查询销售订单
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            trade_status: 订单状态 (5=已取消, 30=待审核)
            owner_no: 货主编号（可选）
            warehouse_no: 仓库编号（可选）
            is_exist_flag: 标记过滤 (0=全部, 1=存在标记, 2=不存在标记)
            page_size: 每页大小（最大100）
            page_no: 页码（从0开始）
            
        Returns:
            查询结果
            
        Raises:
            WDTSalesTradeAPIException: API调用失败
        """
        try:
            # 验证时间跨度（最大1天）
            time_diff = end_time - start_time
            if time_diff.days > 1:
                raise ValueError("时间跨度不能超过1天")
            
            self.logger.info(f"查询销售订单: {start_time} - {end_time}, 状态: {trade_status}")
            
            # 调用API
            response = self.client.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                trade_status=trade_status,
                owner_no=owner_no,
                warehouse_no=warehouse_no,
                is_exist_flag=is_exist_flag,
                page_no=page_no,
                page_size=page_size
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"查询销售订单失败: {e}")
            raise
    
    def query_all_pages(
        self,
        start_time: datetime,
        end_time: datetime,
        trade_status: int = 30,
        owner_no: Optional[str] = None,
        warehouse_no: Optional[str] = None,
        is_exist_flag: Optional[int] = None,
        page_size: int = 100,
        max_pages: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        查询所有页面的销售订单
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            trade_status: 订单状态
            owner_no: 货主编号（可选）
            warehouse_no: 仓库编号（可选）
            is_exist_flag: 标记过滤（可选）
            page_size: 每页大小
            max_pages: 最大页数（可选，用于限制查询）
            
        Returns:
            所有订单列表
        """
        all_trades = []
        page_no = 0
        
        while True:
            try:
                # 查询当前页
                response = self.query_trades(
                    start_time=start_time,
                    end_time=end_time,
                    trade_status=trade_status,
                    owner_no=owner_no,
                    warehouse_no=warehouse_no,
                    is_exist_flag=is_exist_flag,
                    page_size=page_size,
                    page_no=page_no
                )
                
                # 获取订单列表
                content = response.get('content', [])
                if not content:
                    break
                
                all_trades.extend(content)
                
                # 检查是否还有更多页面
                total_count = response.get('total', 0)
                if page_no == 0:
                    self.logger.info(f"总记录数: {total_count}")
                
                # 计算是否还有下一页
                current_count = (page_no + 1) * page_size
                if current_count >= total_count:
                    break
                
                # 检查最大页数限制
                if max_pages and page_no + 1 >= max_pages:
                    self.logger.warning(f"达到最大页数限制: {max_pages}")
                    break
                
                page_no += 1
                
            except Exception as e:
                self.logger.error(f"查询第{page_no}页失败: {e}")
                break
        
        self.logger.info(f"共查询到 {len(all_trades)} 条订单记录")
        return all_trades
    
    def query_by_hour_segments(
        self,
        start_time: datetime,
        end_time: datetime,
        segment_hours: int = 12,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        按小时段分段查询（用于处理大时间跨度）
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            segment_hours: 每段小时数（最大24小时）
            **kwargs: 其他查询参数
            
        Returns:
            所有订单列表
        """
        all_trades = []
        current_start = start_time
        
        # 限制每段最大24小时
        segment_hours = min(segment_hours, 24)
        
        while current_start < end_time:
            # 计算当前段的结束时间
            current_end = min(
                current_start + timedelta(hours=segment_hours),
                end_time
            )
            
            self.logger.info(f"查询时间段: {current_start} - {current_end}")
            
            try:
                # 查询当前时间段
                segment_trades = self.query_all_pages(
                    start_time=current_start,
                    end_time=current_end,
                    **kwargs
                )
                
                all_trades.extend(segment_trades)
                
            except Exception as e:
                self.logger.error(f"查询时间段失败 {current_start} - {current_end}: {e}")
            
            # 移动到下一个时间段
            current_start = current_end
        
        self.logger.info(f"分段查询完成，共 {len(all_trades)} 条记录")
        return all_trades
    
    def get_trade_summary(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取订单汇总信息
        
        Args:
            trades: 订单列表
            
        Returns:
            汇总信息
        """
        if not trades:
            return {
                'total_count': 0,
                'total_amount': 0,
                'total_goods_count': 0,
                'status_summary': {}
            }
        
        total_amount = 0
        total_goods_count = 0
        status_summary = {}
        
        for trade in trades:
            # 统计金额
            amount = float(trade.get('total_amount', 0))
            total_amount += amount
            
            # 统计商品数量
            goods_count = float(trade.get('goods_count', 0))
            total_goods_count += goods_count
            
            # 统计状态分布
            status = trade.get('trade_status')
            if status:
                status_summary[status] = status_summary.get(status, 0) + 1
        
        return {
            'total_count': len(trades),
            'total_amount': round(total_amount, 2),
            'total_goods_count': round(total_goods_count, 2),
            'status_summary': status_summary
        }
    
    def filter_trades_by_criteria(
        self,
        trades: List[Dict[str, Any]],
        min_amount: Optional[float] = None,
        max_amount: Optional[float] = None,
        shop_nos: Optional[List[str]] = None,
        warehouse_nos: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        根据条件过滤订单
        
        Args:
            trades: 订单列表
            min_amount: 最小金额
            max_amount: 最大金额
            shop_nos: 店铺编号列表
            warehouse_nos: 仓库编号列表
            
        Returns:
            过滤后的订单列表
        """
        filtered_trades = []
        
        for trade in trades:
            # 金额过滤
            if min_amount is not None:
                amount = float(trade.get('total_amount', 0))
                if amount < min_amount:
                    continue
            
            if max_amount is not None:
                amount = float(trade.get('total_amount', 0))
                if amount > max_amount:
                    continue
            
            # 店铺过滤
            if shop_nos:
                shop_no = trade.get('shop_no')
                if shop_no not in shop_nos:
                    continue
            
            # 仓库过滤
            if warehouse_nos:
                warehouse_no = trade.get('warehouse_no')
                if warehouse_no not in warehouse_nos:
                    continue
            
            filtered_trades.append(trade)
        
        return filtered_trades
