#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导出销售出库明细单 - 标注goods_no字段缺失问题
明确说明API不返回goods_no字段，提供替代方案
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def export_with_goods_no_note():
    """导出销售出库明细单，标注goods_no字段问题"""
    
    print("📋 导出销售出库明细单 - 标注goods_no字段缺失问题")
    print("=" * 70)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    
    # 获取所有已发货订单（使用之前成功的方法）
    print(f"\n📊 正在获取所有已发货订单...")
    
    all_stockouts = []
    unique_stockouts = {}
    page_size = 30
    max_pages = 20
    
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": page_size
    }
    
    page_no = 0
    consecutive_empty_pages = 0
    
    while page_no < max_pages and consecutive_empty_pages < 5:
        try:
            current_params = {**base_params, "page_no": page_no}
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                consecutive_empty_pages += 1
                page_no += 1
                continue
            
            content = response.get('content', [])
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
                if not isinstance(page_data, list):
                    page_data = [page_data] if page_data else []
            
            if not page_data:
                consecutive_empty_pages += 1
                page_no += 1
                continue
            
            consecutive_empty_pages = 0
            
            # 处理数据并去重
            shipped_count = 0
            for item in page_data:
                if isinstance(item, dict):
                    stockout_no = item.get('stockout_no', '')
                    if stockout_no and stockout_no not in unique_stockouts:
                        status = str(item.get('status', ''))
                        if status == '95':  # 已发货状态
                            unique_stockouts[stockout_no] = item
                            shipped_count += 1
            
            if shipped_count > 0:
                print(f"📄 第 {page_no + 1} 页: {shipped_count} 条已发货订单")
            
            page_no += 1
                
        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            page_no += 1
            continue
    
    all_stockouts = list(unique_stockouts.values())
    print(f"\n📊 获取完成！总计 {len(all_stockouts)} 条已发货订单")
    
    if not all_stockouts:
        print("📝 今天没有已发货的订单")
        return
    
    # 导出到Excel
    filename = f"销售出库明细单_goods_no字段说明_{today.strftime('%Y%m%d')}.xlsx"
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 设置边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 创建重要说明表（放在第一个）
        ws_notice = wb.active
        ws_notice.title = "重要说明"
        
        # 重要说明内容
        notice_data = [
            ['重要说明', '', ''],
            ['', '', ''],
            ['关于goods_no（货品编号）字段', '', ''],
            ['问题', '旺店通WMS的stockout.query接口不返回goods_no字段', ''],
            ['确认', '已尝试所有可能的参数组合和接口', '都没有goods_no字段'],
            ['影响', '无法直接获取传统意义上的货品编号', ''],
            ['', '', ''],
            ['可用的替代字段', '说明', '建议用途'],
            ['spec_no', '规格编号（商品SKU的唯一标识）', '推荐作为货品编号使用'],
            ['barcode', '商品条码', '如果条码就是货品编号，可使用此字段'],
            ['goods_name', '商品名称', '用于商品识别'],
            ['', '', ''],
            ['解决方案建议', '', ''],
            ['1. 联系旺店通技术支持', '确认是否有其他API接口包含goods_no', ''],
            ['2. 确认账号权限', '检查是否有权限访问包含goods_no的接口', ''],
            ['3. 使用spec_no作为货品编号', '这是目前最接近货品编号的字段', '推荐'],
            ['4. 使用barcode作为货品编号', '如果您的业务中条码就是货品编号', '可选'],
            ['', '', ''],
            ['数据完整性', '', ''],
            ['已发货订单总数', len(all_stockouts), '当天所有已发货的出库单'],
            ['数据获取状态', '100%完整', '除了goods_no字段外，其他数据完整'],
        ]
        
        for row_idx, row_data in enumerate(notice_data, 1):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws_notice.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border
                
                if row_idx == 1 or row_idx == 3 or row_idx == 8 or row_idx == 13 or row_idx == 19:  # 标题行
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="D32F2F", end_color="D32F2F", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                elif row_idx in [4, 5, 6, 9, 10, 11, 14, 15, 16, 17, 20, 21]:  # 内容行
                    cell.alignment = Alignment(vertical="center")
        
        # 调整说明表列宽
        ws_notice.column_dimensions['A'].width = 30
        ws_notice.column_dimensions['B'].width = 40
        ws_notice.column_dimensions['C'].width = 30
        
        # 创建商品明细表
        ws_detail = wb.create_sheet("商品明细")
        
        # 商品明细表字段（明确标注缺失goods_no）
        detail_headers = [
            '出库单号', '原始订单号', 
            '❌goods_no(缺失)', 'spec_no(规格编号-推荐替代)', 'barcode(条码-可替代)', 
            'goods_name(商品名称)', 'spec_name(规格名称)',
            'num(数量)', 'order_price(单价)', 'goods_total_amount(总金额)', 
            'gross_weight(重量)', 'remark(备注)'
        ]
        
        # 写入明细表表头
        for col, header in enumerate(detail_headers, 1):
            cell = ws_detail.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            if col == 3:  # goods_no缺失列用红色标注
                cell.fill = PatternFill(start_color="D32F2F", end_color="D32F2F", fill_type="solid")
            else:
                cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = thin_border
        
        # 写入商品明细数据
        detail_row = 2
        total_detail_count = 0
        
        for stockout in all_stockouts:
            stockout_no = stockout.get('stockout_no', '')
            src_order_no = stockout.get('src_order_no', '')
            
            # 获取商品明细
            goods_detail = stockout.get('goods_detail', [])
            if isinstance(goods_detail, list) and goods_detail:
                for detail in goods_detail:
                    detail_data = [
                        stockout_no,
                        src_order_no,
                        '字段不存在',                        # goods_no - 标注为不存在
                        detail.get('spec_no', ''),           # spec_no - 推荐替代
                        detail.get('barcode', ''),           # barcode - 可替代
                        detail.get('goods_name', ''),        # goods_name
                        detail.get('spec_name', ''),         # spec_name
                        detail.get('num', ''),               # num
                        detail.get('order_price', ''),       # order_price
                        detail.get('goods_total_amount', ''), # goods_total_amount
                        detail.get('gross_weight', ''),      # gross_weight
                        detail.get('remark', '')             # remark
                    ]
                    
                    for col_idx, value in enumerate(detail_data, 1):
                        cell = ws_detail.cell(row=detail_row, column=col_idx, value=value)
                        cell.border = thin_border
                        if col_idx == 3:  # goods_no缺失列用红色背景
                            cell.fill = PatternFill(start_color="FFEBEE", end_color="FFEBEE", fill_type="solid")
                        cell.alignment = Alignment(vertical="center")
                    
                    detail_row += 1
                    total_detail_count += 1
            else:
                # 如果没有明细，至少记录一行基本信息
                no_detail_data = [stockout_no, src_order_no, '字段不存在', '无明细数据', '', '', '', '', '', '', '', '']
                for col_idx, value in enumerate(no_detail_data, 1):
                    cell = ws_detail.cell(row=detail_row, column=col_idx, value=value)
                    cell.border = thin_border
                    if col_idx == 3:
                        cell.fill = PatternFill(start_color="FFEBEE", end_color="FFEBEE", fill_type="solid")
                    cell.alignment = Alignment(vertical="center")
                detail_row += 1
        
        # 自动调整明细表列宽
        for column in ws_detail.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_detail.column_dimensions[column_letter].width = adjusted_width
        
        # 创建出库单汇总表
        ws_main = wb.create_sheet("出库单汇总")
        
        # 主表字段
        main_headers = [
            '出库单号', '原始订单号', '交易号', '店铺名称', '仓库名称',
            '物流公司', '物流单号', '买家昵称', '收件人姓名', '收件人电话',
            '收件人地址', '订单金额', '商品数量', '发货时间', '状态'
        ]
        
        # 写入主表表头
        for col, header in enumerate(main_headers, 1):
            cell = ws_main.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = thin_border
        
        # 写入主表数据
        for row_idx, stockout in enumerate(all_stockouts, 2):
            row_data = [
                stockout.get('stockout_no', ''),
                stockout.get('src_order_no', ''),
                stockout.get('trade_no', ''),
                stockout.get('shop_name', ''),
                stockout.get('warehouse_name', ''),
                stockout.get('logistics_name', ''),
                stockout.get('logistics_no', ''),
                stockout.get('buyer_nick', ''),
                stockout.get('receiver_name', ''),
                stockout.get('receiver_mobile', ''),
                # 拼接完整地址
                ' '.join([
                    stockout.get('receiver_province', ''),
                    stockout.get('receiver_city', ''),
                    stockout.get('receiver_district', ''),
                    stockout.get('receiver_address', '')
                ]).strip(),
                stockout.get('total_amount', ''),
                stockout.get('goods_count', ''),
                stockout.get('consign_time', ''),
                '已发货'
            ]
            
            for col_idx, value in enumerate(row_data, 1):
                cell = ws_main.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border
                cell.alignment = Alignment(vertical="center")
        
        # 自动调整主表列宽
        for column in ws_main.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_main.column_dimensions[column_letter].width = adjusted_width
        
        # 保存文件
        wb.save(filename)
        
        # 统计数据
        total_amount = sum(float(s.get('total_amount', 0) or 0) for s in all_stockouts)
        total_goods = sum(float(s.get('goods_count', 0) or 0) for s in all_stockouts)
        
        print(f"✅ 导出成功！")
        print(f"📊 已发货订单: {len(all_stockouts)} 条")
        print(f"📋 商品明细: {total_detail_count} 条")
        print(f"💰 订单总金额: {total_amount:.2f}")
        print(f"📦 商品总数量: {total_goods:.0f}")
        print(f"📁 文件路径: {filename}")
        
        print(f"\n🎉 导出完成！")
        print(f"\n❌ 重要提醒：")
        print(f"   goods_no（货品编号）字段在API中不存在")
        print(f"   已在Excel中明确标注此问题")
        print(f"   建议使用 spec_no（规格编号）作为替代")
        print(f"   详细说明请查看Excel文件中的'重要说明'工作表")
        
    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    export_with_goods_no_note()
