#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查是否有其他API可以获取货品编号信息
尝试通过商品查询接口获取货品编号
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def check_goods_api():
    """检查是否有其他API可以获取货品编号信息"""
    
    print("🔍 检查是否有其他API可以获取货品编号信息")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 从出库单中获取一些spec_no用于查询
    spec_nos = [
        "1683214231569040384",  # COLORFIRE 桌面音响 CSP-5201-黑色
        "1797735757663568128",  # 蒸汽波鼠标垫
        "1971682119432045696",  # COLORFIRE 桌面音响 FS-T2201-BT白色
    ]
    
    print(f"🔍 尝试通过不同的API查询货品信息...")
    
    # 尝试1: 商品查询接口
    print(f"\n📦 尝试1: 使用 goods.query 接口")
    try:
        response = client.call_api('goods.query', {
            "page_size": 10,
            "page_no": 0
        })
        
        if response and 'content' in response:
            content = response.get('content', [])
            if isinstance(content, list) and content:
                print(f"✅ goods.query 接口调用成功，返回 {len(content)} 条数据")
                
                # 分析第一条商品数据的字段
                first_goods = content[0]
                print(f"📋 商品数据字段:")
                for key, value in first_goods.items():
                    print(f"  {key}: {value}")
                
                # 检查是否有货品编号相关字段
                goods_fields = ['goods_no', 'goods_code', 'product_no', 'item_no', 'sku_no']
                print(f"\n🔍 检查货品编号相关字段:")
                for field in goods_fields:
                    if field in first_goods:
                        print(f"  ✅ {field}: {first_goods.get(field)}")
                    else:
                        print(f"  ❌ {field}: 不存在")
            else:
                print(f"❌ goods.query 接口返回数据为空")
        else:
            print(f"❌ goods.query 接口调用失败")
    except Exception as e:
        print(f"❌ goods.query 接口调用异常: {e}")
    
    # 尝试2: 规格查询接口
    print(f"\n📦 尝试2: 使用 spec.query 接口")
    try:
        response = client.call_api('spec.query', {
            "page_size": 10,
            "page_no": 0
        })
        
        if response and 'content' in response:
            content = response.get('content', [])
            if isinstance(content, list) and content:
                print(f"✅ spec.query 接口调用成功，返回 {len(content)} 条数据")
                
                # 分析第一条规格数据的字段
                first_spec = content[0]
                print(f"📋 规格数据字段:")
                for key, value in first_spec.items():
                    print(f"  {key}: {value}")
                
                # 检查是否有货品编号相关字段
                spec_fields = ['goods_no', 'goods_code', 'spec_no', 'spec_code']
                print(f"\n🔍 检查货品编号相关字段:")
                for field in spec_fields:
                    if field in first_spec:
                        print(f"  ✅ {field}: {first_spec.get(field)}")
                    else:
                        print(f"  ❌ {field}: 不存在")
            else:
                print(f"❌ spec.query 接口返回数据为空")
        else:
            print(f"❌ spec.query 接口调用失败")
    except Exception as e:
        print(f"❌ spec.query 接口调用异常: {e}")
    
    # 尝试3: 通过spec_no查询具体商品信息
    print(f"\n📦 尝试3: 通过spec_no查询具体商品信息")
    for spec_no in spec_nos[:1]:  # 只测试第一个
        try:
            print(f"\n🔍 查询 spec_no: {spec_no}")
            
            # 尝试通过spec.query查询特定规格
            response = client.call_api('spec.query', {
                "spec_no": spec_no,
                "page_size": 1,
                "page_no": 0
            })
            
            if response and 'content' in response:
                content = response.get('content', [])
                if isinstance(content, list) and content:
                    spec_data = content[0]
                    print(f"✅ 找到规格数据:")
                    for key, value in spec_data.items():
                        print(f"  {key}: {value}")
                else:
                    print(f"❌ 未找到该规格的数据")
            else:
                print(f"❌ 查询失败")
                
        except Exception as e:
            print(f"❌ 查询异常: {e}")
    
    # 总结分析
    print(f"\n📝 分析总结:")
    print(f"根据目前的分析，在旺店通WMS的stockout.query接口中:")
    print(f"1. 商品明细只包含以下字段:")
    print(f"   - spec_no: 规格编号（商品SKU的唯一标识）")
    print(f"   - spec_name: 规格名称")
    print(f"   - goods_name: 商品名称")
    print(f"   - barcode: 条码")
    print(f"   - 其他物理属性字段")
    print(f"")
    print(f"2. 在旺店通WMS系统中，可能的情况是:")
    print(f"   - spec_no 就是商品的唯一标识（相当于货品编号）")
    print(f"   - barcode 是商品的条码标识")
    print(f"   - 没有单独的 goods_no 字段")
    print(f"")
    print(f"3. 建议:")
    print(f"   - 使用 spec_no 作为货品编号")
    print(f"   - 使用 barcode 作为条码")
    print(f"   - 如果需要更详细的商品信息，可能需要通过其他接口获取")

if __name__ == "__main__":
    check_goods_api()
