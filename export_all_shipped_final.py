#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导出当天所有已发货的出库单
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def export_all_shipped_stockouts():
    """导出当天所有已发货的出库单"""
    
    print("🚚 导出当天所有已发货的出库单")
    print("=" * 50)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 获取当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    # 如果是当天，结束时间不超过当前时间
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 查询参数
    params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_no": 0,
        "page_size": 100
    }
    
    try:
        # 获取第一页数据以确定总记录数
        response = client.call_api('stockout.query', params)
        total = int(response.get('total', 0))
        
        if total == 0:
            print("📝 今天没有出库单数据")
            return
            
        print(f"📈 总记录数: {total} 条")
        
        all_stockouts = []
        page_no = 0
        
        while len(all_stockouts) < total:
            try:
                # 更新分页参数
                params['page_no'] = page_no
                response = client.call_api('stockout.query', params)
                
                content = response.get('content', [])
                if isinstance(content, dict):
                    stockouts = content.get('content', []) or content.get('stockouts', [])
                else:
                    stockouts = content
                
                print(f"   📊 第{page_no + 1}页: {len(stockouts)} 条 (已获取 {len(all_stockouts)}/{total})")
                
                if not stockouts:
                    break
                    
                all_stockouts.extend(stockouts)
                page_no += 1
                
            except Exception as e:
                print(f"   ❌ 获取第{page_no + 1}页失败: {e}")
                break
        
        print(f"\n✅ 成功获取 {len(all_stockouts)} 条出库单")
        
        # 导出到Excel
        filename = f"已发货出库单_全部_{today.strftime('%Y%m%d')}.xlsx"
        print(f"\n📊 开始导出到Excel文件: {filename}")
        
        from openpyxl import Workbook
        from openpyxl.styles import Font, Alignment
        
        wb = Workbook()
        ws = wb.active
        ws.title = "出库单明细"
        
        # 写入表头
        headers = list(all_stockouts[0].keys())
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal="center")
        
        # 写入数据
        for row_idx, stockout in enumerate(all_stockouts, 2):
            for col_idx, key in enumerate(headers, 1):
                value = stockout.get(key, "")
                if isinstance(value, (list, dict)):
                    value = json.dumps(value, ensure_ascii=False)
                ws.cell(row=row_idx, column=col_idx, value=value)
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = max(len(str(cell.value)) for cell in column)
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column[0].column_letter].width = adjusted_width
        
        wb.save(filename)
        print(f"🎉 导出完成! 文件已保存为: {filename}")
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    export_all_shipped_stockouts()
