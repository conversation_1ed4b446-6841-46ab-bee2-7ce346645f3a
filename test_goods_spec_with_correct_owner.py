#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用正确的货主编号测试 goods.spec.query.step 接口
"""

import json
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient

def test_goods_spec_with_correct_owner():
    """使用正确的货主编号测试 goods.spec.query.step 接口"""
    
    print("🔍 使用正确的货主编号测试 goods.spec.query.step 接口")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 正确的货主编号
    owner_no = "AJT-XLSWDT"
    print(f"📦 使用货主编号: {owner_no}")
    
    # 设置时间参数（限制在1小时内）
    now = datetime.now()
    start_time = now - timedelta(minutes=30)  # 30分钟前
    end_time = now

    print(f"📅 时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏰ 时间跨度: {(end_time - start_time).total_seconds() / 60:.1f} 分钟")

    # 尝试不同的参数组合（都在1小时内）
    test_params = [
        # 基础查询（30分钟时间窗口）
        {
            "owner_no": owner_no,
            "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 10,
            "page_no": 0
        },

        # 1小时时间窗口
        {
            "owner_no": owner_no,
            "start_time": (now - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": now.strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 10,
            "page_no": 0
        },

        # 15分钟时间窗口
        {
            "owner_no": owner_no,
            "start_time": (now - timedelta(minutes=15)).strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": now.strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 10,
            "page_no": 0
        },

        # 带状态参数（30分钟窗口）
        {
            "owner_no": owner_no,
            "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 10,
            "page_no": 0,
            "status": 1
        },

        # 尝试今天早上的1小时
        {
            "owner_no": owner_no,
            "start_time": now.replace(hour=8, minute=0, second=0).strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": now.replace(hour=9, minute=0, second=0).strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 10,
            "page_no": 0
        },

        # 尝试昨天的1小时
        {
            "owner_no": owner_no,
            "start_time": (now - timedelta(days=1)).replace(hour=10, minute=0, second=0).strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": (now - timedelta(days=1)).replace(hour=11, minute=0, second=0).strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 10,
            "page_no": 0
        }
    ]
    
    successful_params = None
    goods_data = None
    
    for i, params in enumerate(test_params, 1):
        print(f"\n📋 尝试参数组合 {i}: {params}")
        
        try:
            response = client.call_api('goods.spec.query.step', params)
            
            if response and 'content' in response:
                content = response.get('content', [])
                total = response.get('total', 0)
                
                print(f"✅ API调用成功: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
                
                if isinstance(content, list) and content:
                    first_item = content[0]
                    print(f"📋 第一条数据的所有字段:")
                    
                    # 查找货品编号和备注相关字段
                    goods_no_fields = []
                    remark_fields = []
                    spec_fields = []
                    
                    for key, value in first_item.items():
                        if 'goods_no' in key.lower() or 'product_no' in key.lower() or 'item_no' in key.lower():
                            goods_no_fields.append((key, value))
                            print(f"  🔍 货品编号字段 {key}: {value}")
                        elif 'remark' in key.lower() or 'note' in key.lower() or 'memo' in key.lower() or 'desc' in key.lower():
                            remark_fields.append((key, value))
                            print(f"  📝 备注字段 {key}: {value}")
                        elif 'spec' in key.lower():
                            spec_fields.append((key, value))
                            print(f"  📦 规格字段 {key}: {value}")
                        else:
                            print(f"    {key}: {value}")
                    
                    print(f"\n✅ 找到的关键字段:")
                    print(f"  货品编号字段: {[field[0] for field in goods_no_fields]}")
                    print(f"  备注字段: {[field[0] for field in remark_fields]}")
                    print(f"  规格字段: {[field[0] for field in spec_fields]}")
                    
                    successful_params = params
                    goods_data = content
                    
                    # 保存成功的数据
                    success_filename = f"goods_spec_success_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    with open(success_filename, 'w', encoding='utf-8') as f:
                        json.dump({
                            'api_name': 'goods.spec.query.step',
                            'owner_no': owner_no,
                            'params': params,
                            'response': response,
                            'goods_no_fields': goods_no_fields,
                            'remark_fields': remark_fields,
                            'spec_fields': spec_fields
                        }, f, ensure_ascii=False, indent=2)
                    
                    print(f"📁 成功数据已保存到: {success_filename}")
                    break
                
                elif isinstance(content, dict):
                    print(f"📋 返回的是字典格式:")
                    for key, value in content.items():
                        print(f"  {key}: {value}")
                
                else:
                    print(f"❌ 返回数据格式异常")
            
            else:
                print(f"❌ API返回数据为空或格式错误")
        
        except Exception as e:
            print(f"❌ 参数组合 {i} 调用失败: {e}")
    
    # 总结结果
    print(f"\n📝 测试结果总结:")
    if successful_params and goods_data:
        print(f"✅ goods.spec.query.step 接口测试成功！")
        print(f"✅ 正确的货主编号: {owner_no}")
        print(f"✅ 成功的参数: {successful_params}")
        print(f"✅ 获取到 {len(goods_data)} 条商品数据")
        
        # 分析数据结构
        if goods_data:
            sample_item = goods_data[0]
            print(f"\n📋 数据结构分析:")
            
            # 统计有备注的商品
            remark_count = 0
            for item in goods_data:
                for key, value in item.items():
                    if 'remark' in key.lower() and value and str(value).strip():
                        remark_count += 1
                        break
            
            print(f"📊 总商品数: {len(goods_data)}")
            print(f"📝 有备注的商品数: {remark_count}")
            print(f"📊 备注覆盖率: {remark_count/len(goods_data)*100:.1f}%")
        
        return owner_no, successful_params, goods_data
    
    else:
        print(f"❌ goods.spec.query.step 接口测试失败")
        print(f"可能的原因:")
        print(f"1. 时间参数格式不正确")
        print(f"2. 需要其他必需参数")
        print(f"3. 接口权限有限制")
        
        return None, None, None

if __name__ == "__main__":
    test_goods_spec_with_correct_owner()
