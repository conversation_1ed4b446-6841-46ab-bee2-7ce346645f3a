#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导出当日所有已发货的销售订单（确保获取完整数据）
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def export_complete_shipped_today():
    """导出当日所有已发货的销售订单（确保完整）"""
    
    print("🚛 导出当日所有已发货的销售订单（完整版）")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 时间范围: {start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")
    
    # 查询参数
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 100
    }
    
    print(f"🔍 开始查询已发货的出库单...")
    
    # 先获取总数
    print(f"📊 正在获取数据总数...")
    try:
        first_response = client.call_api('stockout.query', {
            **base_params,
            "page_no": 0,
            "page_size": 1
        })
        total_records = first_response.get('total', 0)
        # 确保total_records是整数
        if isinstance(total_records, str):
            try:
                total_records = int(total_records)
            except:
                total_records = 1000
        print(f"📈 API返回总记录数: {total_records}")
    except Exception as e:
        print(f"❌ 获取总数失败: {e}")
        total_records = 1000  # 使用一个较大的默认值

    all_stockouts = []
    page_no = 0
    max_pages = (total_records // base_params["page_size"]) + 2  # 多查询几页确保完整
    
    print(f"📑 预计需要查询 {max_pages} 页数据")
    
    while page_no < max_pages:
        print(f"\n📄 正在获取第 {page_no + 1} 页数据...")
        
        try:
            # 当前页参数
            current_params = {
                **base_params,
                "page_no": page_no
            }
            
            # 调用API
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                print(f"❌ 第 {page_no + 1} 页响应为空")
                break
            
            # 获取数据
            content = response.get('content', [])
            total = response.get('total', 0)
            
            print(f"📊 第 {page_no + 1} 页: API返回 total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
            
            # 处理数据
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
                if not isinstance(page_data, list):
                    page_data = [page_data] if page_data else []
            
            if not page_data:
                print(f"📝 第 {page_no + 1} 页无数据，可能已到末尾")
                # 连续3页无数据才停止
                if page_no > 0:
                    empty_count = 0
                    for check_page in range(max(0, page_no-2), page_no+1):
                        if check_page not in [p for p in range(page_no+1)]:
                            empty_count += 1
                    if empty_count >= 3:
                        break
                page_no += 1
                continue
            
            # 筛选已发货的数据（status=95）
            shipped_data = []
            for item in page_data:
                if isinstance(item, dict):
                    status = str(item.get('status', ''))
                    if status == '95':  # 已发货状态
                        shipped_data.append(item)
            
            print(f"✅ 第 {page_no + 1} 页: 总数据 {len(page_data)} 条, 已发货 {len(shipped_data)} 条")
            
            if shipped_data:
                all_stockouts.extend(shipped_data)
                print(f"📈 累计已发货订单: {len(all_stockouts)} 条")
            
            page_no += 1
            
            # 安全限制：最多查询200页
            if page_no >= 200:
                print(f"⚠️ 已查询200页，停止查询")
                break
                
        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            page_no += 1
            continue
    
    print(f"\n📊 查询完成！总计获取 {len(all_stockouts)} 条已发货订单")
    
    if not all_stockouts:
        print("📝 今天没有已发货的订单")
        return
    
    # 导出到Excel
    filename = f"当日完整已发货订单_{today.strftime('%Y%m%d')}.xlsx"
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 创建主表
        ws_main = wb.active
        ws_main.title = "已发货订单明细"
        
        # 分析数据结构
        sample = all_stockouts[0]
        
        # 定义主要字段
        main_fields = [
            'stockout_no', 'src_order_no', 'trade_no', 'src_tids',
            'owner_no', 'warehouse_no', 'warehouse_name', 'shop_name', 'shop_no',
            'logistics_code', 'logistics_name', 'logistics_no', 'express_no',
            'buyer_nick', 'receiver_name', 'receiver_mobile', 'receiver_telno',
            'receiver_province', 'receiver_city', 'receiver_district', 'receiver_address',
            'total_amount', 'goods_count', 'goods_type_count', 'weight', 'volume',
            'remark', 'cs_remark', 'buyer_message', 'flag_name',
            'consign_time', 'created', 'modified', 'status',
            'trade_time', 'pay_time', 'platform_name'
        ]
        
        # 使用实际存在的字段
        headers = []
        for field in main_fields:
            if field in sample:
                headers.append(field)
        
        # 添加其他存在的字段
        for key in sample.keys():
            if key not in headers and not isinstance(sample[key], (dict, list)):
                headers.append(key)
        
        print(f"📋 导出字段: {len(headers)} 个")
        
        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws_main.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 写入数据
        for row_idx, stockout in enumerate(all_stockouts, 2):
            for col_idx, header in enumerate(headers, 1):
                value = stockout.get(header, '')
                
                # 处理复杂数据类型
                if isinstance(value, (dict, list)):
                    value = json.dumps(value, ensure_ascii=False)
                elif value is None:
                    value = ''
                
                ws_main.cell(row=row_idx, column=col_idx, value=str(value))
        
        # 自动调整列宽
        for column in ws_main.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_main.column_dimensions[column_letter].width = adjusted_width
        
        # 创建汇总表
        ws_summary = wb.create_sheet("数据汇总")
        
        # 汇总表头
        summary_headers = ['统计项目', '数值', '说明']
        for col, header in enumerate(summary_headers, 1):
            cell = ws_summary.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 统计数据
        total_amount = 0
        total_goods = 0
        logistics_stats = {}
        warehouse_stats = {}
        
        for stockout in all_stockouts:
            # 金额统计
            amount = stockout.get('total_amount', 0)
            if amount is None:
                amount = 0
            elif isinstance(amount, str):
                try:
                    amount = float(amount)
                except:
                    amount = 0
            elif not isinstance(amount, (int, float)):
                amount = 0
            total_amount += amount
            
            # 商品数量统计
            goods_count = stockout.get('goods_count', 0)
            if goods_count is None:
                goods_count = 0
            elif isinstance(goods_count, str):
                try:
                    goods_count = float(goods_count)
                except:
                    goods_count = 0
            elif not isinstance(goods_count, (int, float)):
                goods_count = 0
            total_goods += goods_count
            
            # 物流公司统计
            logistics = stockout.get('logistics_name', '未知')
            logistics_stats[logistics] = logistics_stats.get(logistics, 0) + 1
            
            # 仓库统计
            warehouse = stockout.get('warehouse_name', '未知')
            warehouse_stats[warehouse] = warehouse_stats.get(warehouse, 0) + 1
        
        # 写入汇总数据
        summary_data = [
            ('查询日期', today.strftime('%Y-%m-%d'), '数据查询的日期'),
            ('导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '文件生成时间'),
            ('已发货订单总数', len(all_stockouts), '状态为95的出库单数量'),
            ('订单总金额', f'{total_amount:.2f}', '所有订单金额汇总'),
            ('商品总数量', f'{total_goods:.0f}', '所有商品数量汇总'),
            ('主要物流公司', max(logistics_stats.items(), key=lambda x: x[1])[0] if logistics_stats else '无', '使用最多的物流公司'),
            ('主要发货仓库', max(warehouse_stats.items(), key=lambda x: x[1])[0] if warehouse_stats else '无', '发货最多的仓库'),
            ('查询页数', page_no, '实际查询的API页数'),
            ('API总记录数', total_records, 'API返回的总记录数')
        ]
        
        for row_idx, (item, value, desc) in enumerate(summary_data, 2):
            ws_summary.cell(row=row_idx, column=1, value=item)
            ws_summary.cell(row=row_idx, column=2, value=str(value))
            ws_summary.cell(row=row_idx, column=3, value=desc)
        
        # 保存文件
        wb.save(filename)
        
        print(f"✅ 导出成功！")
        print(f"📊 总计导出: {len(all_stockouts)} 条已发货订单")
        print(f"💰 订单总金额: {total_amount:.2f}")
        print(f"📦 商品总数量: {total_goods:.0f}")
        print(f"📁 文件路径: {filename}")
        
        # 显示物流公司分布
        if logistics_stats:
            print(f"\n🚚 物流公司分布:")
            for logistics, count in sorted(logistics_stats.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"   {logistics}: {count} 单")
        
        # 显示仓库分布
        if warehouse_stats:
            print(f"\n🏪 发货仓库分布:")
            for warehouse, count in sorted(warehouse_stats.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"   {warehouse}: {count} 单")
        
        print(f"\n🎉 当日所有已发货订单导出完成！")
        print(f"💡 提示: 如果实际获取数量少于API总数，可能需要调整查询策略")
        
    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    export_complete_shipped_today()
