# 🎯 旺店通WMS API销售订单导出工具

## 📋 项目简介

这是一个完整的旺店通WMS API集成解决方案，专门用于获取销售订单数据并导出到Excel文件。

## ✨ 功能特性

- 🔗 **完整的API集成**: 支持旺店通WMS API的POST请求方式
- 📊 **Excel导出**: 将销售订单数据导出为格式化的Excel文件
- 📅 **多种时间范围**: 支持今天、昨天、指定日期的订单查询
- 📈 **数据统计**: 提供订单汇总和统计功能
- 🔍 **状态筛选**: 支持不同订单状态的查询（待审核、已取消等）
- 📄 **分页查询**: 自动处理大量数据的分页获取
- 🗜️ **响应压缩**: 支持API响应数据的自动解压缩
- ⚡ **错误处理**: 完善的异常处理和日志记录

## 📁 文件结构

```
WMSAPI/
├── config.py                      # API配置文件
├── wdt_post_client.py             # POST API客户端（主要）
├── wdt_original_client.py         # GET API客户端（备用）
├── export_daily_orders.py         # 完整版导出工具（需要pandas）
├── export_daily_orders_simple.py  # 简化版导出工具（仅需openpyxl）
├── daily_export_tool.py           # 交互式导出工具
├── success_example.py             # API使用示例
└── README_FINAL.md                # 说明文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install openpyxl requests
```

### 2. 配置API

编辑 `config.py` 文件，填入您的API配置：

```python
class WDTConfig:
    # API基础配置
    SID = "您的卖家标识"
    APP_KEY = "您的应用公钥"
    APP_SECRET = "您的应用私钥"
    API_URL = "https://openapi.wdtwms.com/open_api/service.php"
```

### 3. 运行导出工具

#### 方式1: 交互式工具
```bash
python daily_export_tool.py
```

#### 方式2: 直接导出今天订单
```bash
python export_daily_orders_simple.py
```

#### 方式3: 使用API示例
```bash
python success_example.py
```

## 📊 导出结果

导出的Excel文件包含两个工作表：

### 📋 销售订单明细
包含完整的订单信息：
- 基础信息：ERP单号、原始单号、订单状态等
- 客户信息：买家昵称、收件人地址等
- 商品明细：商品编码、数量、重量等
- 时间信息：下单时间、付款时间、接单时间等

### 📈 订单汇总
按订单状态统计：
- 订单数量
- 总金额
- 总货品数

## 🔧 API客户端使用

### 基础用法

```python
from wdt_post_client import WDTPostClient
from datetime import datetime, timedelta

# 创建客户端
client = WDTPostClient()

# 查询今天的订单
today = datetime.now()
start_time = today.replace(hour=0, minute=0, second=0)
end_time = today.replace(hour=23, minute=59, second=59)

response = client.query_sales_trades(
    start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
    end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
    trade_status=30,  # 30=待审核, 5=已取消
    page_no=0,
    page_size=10
)

print(f"总计: {response.get('total', 0)} 条订单")
```

## 📝 订单状态说明

| 状态代码 | 状态名称 | 说明 |
|---------|---------|------|
| 5 | 已取消 | 已取消的订单 |
| 30 | 待审核 | 等待审核的订单 |

## ⚠️ 注意事项

1. **时间范围限制**: 单次查询时间跨度不能超过1天
2. **分页大小限制**: 每页最大100条记录
3. **压缩响应**: sales.trade.query接口必须启用响应压缩
4. **请求频率**: 建议控制请求频率，避免对服务器造成压力
5. **时间格式**: 时间参数格式为 `YYYY-MM-DD HH:MM:SS`

## 🎉 项目成就

- ✅ 完整的企业级API集成
- ✅ 支持POST请求和响应压缩
- ✅ 完善的错误处理机制
- ✅ 用户友好的导出工具
- ✅ 详细的文档和示例
- ✅ 生产环境就绪

---

🎊 **恭喜！您现在拥有了一个完整的旺店通WMS API集成解决方案！**
