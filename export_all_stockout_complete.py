#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导出当日所有销售出库明细单（完整版 - 获取所有数据）
确保获取当天所有已发货的出库单数据，包含完整的货品编号和商品明细
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

def export_all_stockout_complete():
    """导出当日所有销售出库明细单（完整版）"""
    
    print("📦 导出当日所有销售出库明细单（完整版）")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 时间范围: {start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")
    
    # 查询参数
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 100  # 每页100条
    }
    
    print(f"🔍 开始查询已发货的出库单...")
    
    all_stockouts = []
    page_no = 0
    max_pages = 200  # 最多查询200页，确保获取所有数据
    
    while page_no < max_pages:
        print(f"\n📄 正在获取第 {page_no + 1} 页数据...")
        
        try:
            # 当前页参数
            current_params = {
                **base_params,
                "page_no": page_no
            }
            
            # 调用API
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                print(f"❌ 第 {page_no + 1} 页响应为空")
                break
            
            # 获取数据
            content = response.get('content', [])
            total = response.get('total', 0)
            
            print(f"📊 API返回: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
            
            # 处理数据
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
                if not isinstance(page_data, list):
                    page_data = [page_data] if page_data else []
            
            if not page_data:
                print(f"📝 第 {page_no + 1} 页无数据，停止查询")
                break
            
            # 筛选已发货的数据（status=95）
            shipped_data = []
            for item in page_data:
                if isinstance(item, dict):
                    status = item.get('status', '')
                    # 确保status是字符串或数字进行比较
                    if str(status) == '95':  # 已发货状态
                        shipped_data.append(item)
            
            print(f"✅ 第 {page_no + 1} 页: 总数据 {len(page_data)} 条, 已发货 {len(shipped_data)} 条")
            
            if shipped_data:
                all_stockouts.extend(shipped_data)
                print(f"📈 累计已发货订单: {len(all_stockouts)} 条")
            
            # 如果当前页数据少于页面大小，说明已经是最后一页
            if len(page_data) < base_params["page_size"]:
                print(f"🏁 已获取完所有数据 (当前页数据:{len(page_data)}, 页面大小:{base_params['page_size']})")
                break
            
            page_no += 1
                
        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            # 继续查询下一页，不要因为单页失败而停止
            page_no += 1
            continue
    
    print(f"\n📊 查询完成！总计获取 {len(all_stockouts)} 条已发货订单")
    
    if not all_stockouts:
        print("📝 今天没有已发货的订单")
        return
    
    # 导出到Excel（包含完整商品明细）
    filename = f"完整销售出库明细单_{today.strftime('%Y%m%d')}.xlsx"
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 创建主表（出库单汇总）
        ws_main = wb.active
        ws_main.title = "出库单汇总"
        
        # 主表字段
        main_headers = [
            '出库单号', '原始订单号', '交易号', '原始交易号', '店铺名称', '仓库名称',
            '物流公司', '物流单号', '买家昵称', '收件人姓名', '收件人电话',
            '收件人地址', '订单金额', '商品数量', '商品种类数', '重量',
            '发货时间', '创建时间', '状态', '备注', '客服备注'
        ]
        
        # 写入主表表头
        for col, header in enumerate(main_headers, 1):
            cell = ws_main.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 写入主表数据
        for row_idx, stockout in enumerate(all_stockouts, 2):
            ws_main.cell(row=row_idx, column=1, value=stockout.get('stockout_no', ''))
            ws_main.cell(row=row_idx, column=2, value=stockout.get('src_order_no', ''))
            ws_main.cell(row=row_idx, column=3, value=stockout.get('trade_no', ''))
            ws_main.cell(row=row_idx, column=4, value=stockout.get('src_tids', ''))
            ws_main.cell(row=row_idx, column=5, value=stockout.get('shop_name', ''))
            ws_main.cell(row=row_idx, column=6, value=stockout.get('warehouse_name', ''))
            ws_main.cell(row=row_idx, column=7, value=stockout.get('logistics_name', ''))
            ws_main.cell(row=row_idx, column=8, value=stockout.get('logistics_no', ''))
            ws_main.cell(row=row_idx, column=9, value=stockout.get('buyer_nick', ''))
            ws_main.cell(row=row_idx, column=10, value=stockout.get('receiver_name', ''))
            ws_main.cell(row=row_idx, column=11, value=stockout.get('receiver_mobile', ''))
            
            # 拼接完整地址
            address_parts = [
                stockout.get('receiver_province', ''),
                stockout.get('receiver_city', ''),
                stockout.get('receiver_district', ''),
                stockout.get('receiver_address', '')
            ]
            full_address = ' '.join([part for part in address_parts if part])
            ws_main.cell(row=row_idx, column=12, value=full_address)
            
            ws_main.cell(row=row_idx, column=13, value=stockout.get('total_amount', ''))
            ws_main.cell(row=row_idx, column=14, value=stockout.get('goods_count', ''))
            ws_main.cell(row=row_idx, column=15, value=stockout.get('goods_type_count', ''))
            ws_main.cell(row=row_idx, column=16, value=stockout.get('weight', ''))
            ws_main.cell(row=row_idx, column=17, value=stockout.get('consign_time', ''))
            ws_main.cell(row=row_idx, column=18, value=stockout.get('created', ''))
            ws_main.cell(row=row_idx, column=19, value='已发货')
            ws_main.cell(row=row_idx, column=20, value=stockout.get('remark', ''))
            ws_main.cell(row=row_idx, column=21, value=stockout.get('cs_remark', ''))
        
        # 自动调整主表列宽
        for column in ws_main.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_main.column_dimensions[column_letter].width = adjusted_width
        
        # 创建商品明细表
        ws_detail = wb.create_sheet("商品明细")
        
        # 商品明细表字段
        detail_headers = [
            '出库单号', '原始订单号', '货品编号', '商品编号', '规格编号', '商品名称', '规格名称',
            '商品数量', '单价', '总金额', '成本价', '重量',
            '条码', '品牌编号', '品牌名称', '分类名称', '是否赠品'
        ]
        
        # 写入明细表表头
        for col, header in enumerate(detail_headers, 1):
            cell = ws_detail.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 写入商品明细数据
        detail_row = 2
        total_detail_count = 0
        
        for stockout in all_stockouts:
            stockout_no = stockout.get('stockout_no', '')
            src_order_no = stockout.get('src_order_no', '')
            
            # 获取商品明细
            goods_detail = stockout.get('goods_detail', [])
            if isinstance(goods_detail, list) and goods_detail:
                for detail in goods_detail:
                    ws_detail.cell(row=detail_row, column=1, value=stockout_no)
                    ws_detail.cell(row=detail_row, column=2, value=src_order_no)
                    ws_detail.cell(row=detail_row, column=3, value=detail.get('goods_no', ''))      # 货品编号
                    ws_detail.cell(row=detail_row, column=4, value=detail.get('spec_no', ''))       # 商品编号
                    ws_detail.cell(row=detail_row, column=5, value=detail.get('spec_id', ''))       # 规格编号
                    ws_detail.cell(row=detail_row, column=6, value=detail.get('goods_name', ''))    # 商品名称
                    ws_detail.cell(row=detail_row, column=7, value=detail.get('spec_name', ''))     # 规格名称
                    ws_detail.cell(row=detail_row, column=8, value=detail.get('goods_count', ''))   # 商品数量
                    ws_detail.cell(row=detail_row, column=9, value=detail.get('sell_price', ''))    # 单价
                    ws_detail.cell(row=detail_row, column=10, value=detail.get('total_amount', '')) # 总金额
                    ws_detail.cell(row=detail_row, column=11, value=detail.get('cost_price', ''))   # 成本价
                    ws_detail.cell(row=detail_row, column=12, value=detail.get('weight', ''))       # 重量
                    ws_detail.cell(row=detail_row, column=13, value=detail.get('barcode', ''))      # 条码
                    ws_detail.cell(row=detail_row, column=14, value=detail.get('brand_no', ''))     # 品牌编号
                    ws_detail.cell(row=detail_row, column=15, value=detail.get('brand_name', ''))   # 品牌名称
                    ws_detail.cell(row=detail_row, column=16, value=detail.get('class_name', ''))   # 分类名称
                    ws_detail.cell(row=detail_row, column=17, value='是' if detail.get('gift_type') == 1 else '否')  # 是否赠品
                    detail_row += 1
                    total_detail_count += 1
            else:
                # 如果没有明细，至少记录一行基本信息
                ws_detail.cell(row=detail_row, column=1, value=stockout_no)
                ws_detail.cell(row=detail_row, column=2, value=src_order_no)
                ws_detail.cell(row=detail_row, column=3, value='无明细数据')
                ws_detail.cell(row=detail_row, column=4, value='无明细数据')
                detail_row += 1
        
        # 自动调整明细表列宽
        for column in ws_detail.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_detail.column_dimensions[column_letter].width = adjusted_width
        
        # 创建统计汇总表
        ws_summary = wb.create_sheet("统计汇总")
        
        # 统计数据
        total_amount = sum(float(s.get('total_amount', 0) or 0) for s in all_stockouts)
        total_goods = sum(float(s.get('goods_count', 0) or 0) for s in all_stockouts)
        
        # 物流公司统计
        logistics_stats = {}
        warehouse_stats = {}
        for stockout in all_stockouts:
            logistics = stockout.get('logistics_name', '未知')
            logistics_stats[logistics] = logistics_stats.get(logistics, 0) + 1
            
            warehouse = stockout.get('warehouse_name', '未知')
            warehouse_stats[warehouse] = warehouse_stats.get(warehouse, 0) + 1
        
        # 写入统计数据
        summary_data = [
            ['统计项目', '数值', '说明'],
            ['查询日期', today.strftime('%Y-%m-%d'), '数据查询的日期'],
            ['导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '文件生成时间'],
            ['已发货订单总数', len(all_stockouts), '状态为95的出库单数量'],
            ['商品明细总数', total_detail_count, '所有商品明细条数'],
            ['订单总金额', f'{total_amount:.2f}', '所有订单金额汇总'],
            ['商品总数量', f'{total_goods:.0f}', '所有商品数量汇总'],
            ['主要物流公司', max(logistics_stats.items(), key=lambda x: x[1])[0] if logistics_stats else '无', '使用最多的物流公司'],
            ['主要发货仓库', max(warehouse_stats.items(), key=lambda x: x[1])[0] if warehouse_stats else '无', '发货最多的仓库']
        ]
        
        for row_idx, row_data in enumerate(summary_data, 1):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws_summary.cell(row=row_idx, column=col_idx, value=value)
                if row_idx == 1:  # 表头
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")
        
        # 保存文件
        wb.save(filename)
        
        print(f"✅ 导出成功！")
        print(f"📊 总计导出: {len(all_stockouts)} 条已发货订单")
        print(f"📋 商品明细: {total_detail_count} 条")
        print(f"💰 订单总金额: {total_amount:.2f}")
        print(f"📦 商品总数量: {total_goods:.0f}")
        print(f"📁 文件路径: {filename}")
        print(f"📋 包含三个工作表:")
        print(f"   - 出库单汇总: 订单基本信息")
        print(f"   - 商品明细: 详细商品信息（含货品编号）")
        print(f"   - 统计汇总: 数据统计信息")
        
        # 显示物流公司分布
        if logistics_stats:
            print(f"\n🚚 物流公司分布:")
            for logistics, count in sorted(logistics_stats.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"   {logistics}: {count} 单")
        
        # 显示仓库分布
        if warehouse_stats:
            print(f"\n🏪 发货仓库分布:")
            for warehouse, count in sorted(warehouse_stats.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"   {warehouse}: {count} 单")
        
        print(f"\n🎉 完整销售出库明细单导出完成！")
        
    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    export_all_stockout_complete()
