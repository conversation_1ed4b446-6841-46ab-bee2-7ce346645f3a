#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
强制获取当日所有销售出库明细单
使用不同的查询策略确保获取所有数据
"""

import json
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient

def export_all_stockout_force():
    """强制获取当日所有销售出库明细单"""
    
    print("🔥 强制获取当日所有销售出库明细单")
    print("=" * 60)
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 时间范围: {start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")
    
    all_stockouts = []
    
    # 策略1: 尝试不同的分页大小
    page_sizes = [50, 100, 200]
    
    for page_size in page_sizes:
        print(f"\n🔍 尝试页面大小: {page_size}")
        
        # 查询参数
        base_params = {
            "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": page_size
        }
        
        page_stockouts = []
        page_no = 0
        max_pages = 20  # 每种页面大小最多查询20页
        
        while page_no < max_pages:
            try:
                # 当前页参数
                current_params = {
                    **base_params,
                    "page_no": page_no
                }
                
                # 调用API
                response = client.call_api('stockout.query', current_params)
                
                if not response:
                    print(f"❌ 第 {page_no + 1} 页响应为空")
                    break
                
                # 获取数据
                content = response.get('content', [])
                total = response.get('total', 0)
                
                # 处理数据
                page_data = []
                if isinstance(content, list):
                    page_data = content
                elif isinstance(content, dict):
                    page_data = content.get('content', [])
                    if not isinstance(page_data, list):
                        page_data = [page_data] if page_data else []
                
                if not page_data:
                    print(f"📝 第 {page_no + 1} 页无数据，停止查询")
                    break
                
                # 筛选已发货的数据（status=95）
                shipped_data = []
                for item in page_data:
                    if isinstance(item, dict):
                        status = str(item.get('status', ''))
                        if status == '95':  # 已发货状态
                            shipped_data.append(item)
                
                print(f"📄 第 {page_no + 1} 页: 总数据 {len(page_data)} 条, 已发货 {len(shipped_data)} 条")
                
                if shipped_data:
                    page_stockouts.extend(shipped_data)
                
                # 如果当前页数据少于页面大小，说明已经是最后一页
                if len(page_data) < page_size:
                    print(f"🏁 页面大小 {page_size} 查询完成，获取 {len(page_stockouts)} 条已发货订单")
                    break
                
                page_no += 1
                    
            except Exception as e:
                print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
                page_no += 1
                continue
        
        # 如果这次查询获得了更多数据，就使用这次的结果
        if len(page_stockouts) > len(all_stockouts):
            all_stockouts = page_stockouts
            print(f"✅ 页面大小 {page_size} 获得最多数据: {len(all_stockouts)} 条")
    
    # 策略2: 如果还是只有少量数据，尝试分时段查询
    if len(all_stockouts) < 100:  # 如果数据少于100条，尝试分时段查询
        print(f"\n🕐 数据量较少({len(all_stockouts)}条)，尝试分时段查询...")
        
        # 按4小时分段查询
        time_segments = []
        current_time = start_time
        while current_time < end_time:
            segment_end = min(current_time + timedelta(hours=4), end_time)
            time_segments.append((current_time, segment_end))
            current_time = segment_end
        
        segment_stockouts = []
        for i, (seg_start, seg_end) in enumerate(time_segments):
            print(f"\n📅 查询时段 {i+1}: {seg_start.strftime('%H:%M:%S')} ~ {seg_end.strftime('%H:%M:%S')}")
            
            seg_params = {
                "start_consign_time": seg_start.strftime('%Y-%m-%d %H:%M:%S'),
                "end_consign_time": seg_end.strftime('%Y-%m-%d %H:%M:%S'),
                "page_size": 100
            }
            
            page_no = 0
            while page_no < 10:  # 每个时段最多查询10页
                try:
                    current_params = {
                        **seg_params,
                        "page_no": page_no
                    }
                    
                    response = client.call_api('stockout.query', current_params)
                    if not response:
                        break
                    
                    content = response.get('content', [])
                    page_data = []
                    if isinstance(content, list):
                        page_data = content
                    elif isinstance(content, dict):
                        page_data = content.get('content', [])
                        if not isinstance(page_data, list):
                            page_data = [page_data] if page_data else []
                    
                    if not page_data:
                        break
                    
                    # 筛选已发货的数据
                    shipped_data = []
                    for item in page_data:
                        if isinstance(item, dict):
                            status = str(item.get('status', ''))
                            if status == '95':
                                shipped_data.append(item)
                    
                    if shipped_data:
                        segment_stockouts.extend(shipped_data)
                        print(f"   📄 第 {page_no + 1} 页: {len(shipped_data)} 条已发货")
                    
                    if len(page_data) < 100:
                        break
                    
                    page_no += 1
                        
                except Exception as e:
                    print(f"   ❌ 时段查询失败: {e}")
                    break
        
        # 去重并合并数据
        if len(segment_stockouts) > len(all_stockouts):
            # 使用出库单号去重
            unique_stockouts = {}
            for stockout in segment_stockouts:
                stockout_no = stockout.get('stockout_no', '')
                if stockout_no and stockout_no not in unique_stockouts:
                    unique_stockouts[stockout_no] = stockout
            
            all_stockouts = list(unique_stockouts.values())
            print(f"✅ 分时段查询获得更多数据: {len(all_stockouts)} 条（去重后）")
    
    print(f"\n📊 最终获取 {len(all_stockouts)} 条已发货订单")
    
    if not all_stockouts:
        print("📝 今天没有已发货的订单")
        return
    
    # 导出到Excel
    filename = f"强制获取销售出库明细单_{today.strftime('%Y%m%d')}.xlsx"
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 创建主表（出库单汇总）
        ws_main = wb.active
        ws_main.title = "出库单汇总"
        
        # 主表字段
        main_headers = [
            '出库单号', '原始订单号', '交易号', '原始交易号', '店铺名称', '仓库名称',
            '物流公司', '物流单号', '买家昵称', '收件人姓名', '收件人电话',
            '收件人地址', '订单金额', '商品数量', '商品种类数', '重量',
            '发货时间', '创建时间', '状态', '备注', '客服备注'
        ]
        
        # 写入主表表头
        for col, header in enumerate(main_headers, 1):
            cell = ws_main.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 写入主表数据
        for row_idx, stockout in enumerate(all_stockouts, 2):
            ws_main.cell(row=row_idx, column=1, value=stockout.get('stockout_no', ''))
            ws_main.cell(row=row_idx, column=2, value=stockout.get('src_order_no', ''))
            ws_main.cell(row=row_idx, column=3, value=stockout.get('trade_no', ''))
            ws_main.cell(row=row_idx, column=4, value=stockout.get('src_tids', ''))
            ws_main.cell(row=row_idx, column=5, value=stockout.get('shop_name', ''))
            ws_main.cell(row=row_idx, column=6, value=stockout.get('warehouse_name', ''))
            ws_main.cell(row=row_idx, column=7, value=stockout.get('logistics_name', ''))
            ws_main.cell(row=row_idx, column=8, value=stockout.get('logistics_no', ''))
            ws_main.cell(row=row_idx, column=9, value=stockout.get('buyer_nick', ''))
            ws_main.cell(row=row_idx, column=10, value=stockout.get('receiver_name', ''))
            ws_main.cell(row=row_idx, column=11, value=stockout.get('receiver_mobile', ''))
            
            # 拼接完整地址
            address_parts = [
                stockout.get('receiver_province', ''),
                stockout.get('receiver_city', ''),
                stockout.get('receiver_district', ''),
                stockout.get('receiver_address', '')
            ]
            full_address = ' '.join([part for part in address_parts if part])
            ws_main.cell(row=row_idx, column=12, value=full_address)
            
            ws_main.cell(row=row_idx, column=13, value=stockout.get('total_amount', ''))
            ws_main.cell(row=row_idx, column=14, value=stockout.get('goods_count', ''))
            ws_main.cell(row=row_idx, column=15, value=stockout.get('goods_type_count', ''))
            ws_main.cell(row=row_idx, column=16, value=stockout.get('weight', ''))
            ws_main.cell(row=row_idx, column=17, value=stockout.get('consign_time', ''))
            ws_main.cell(row=row_idx, column=18, value=stockout.get('created', ''))
            ws_main.cell(row=row_idx, column=19, value='已发货')
            ws_main.cell(row=row_idx, column=20, value=stockout.get('remark', ''))
            ws_main.cell(row=row_idx, column=21, value=stockout.get('cs_remark', ''))
        
        # 自动调整主表列宽
        for column in ws_main.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_main.column_dimensions[column_letter].width = adjusted_width
        
        # 创建商品明细表
        ws_detail = wb.create_sheet("商品明细")
        
        # 商品明细表字段
        detail_headers = [
            '出库单号', '原始订单号', '货品编号', '商品编号', '规格编号', '商品名称', '规格名称',
            '商品数量', '单价', '总金额', '成本价', '重量',
            '条码', '品牌编号', '品牌名称', '分类名称', '是否赠品'
        ]
        
        # 写入明细表表头
        for col, header in enumerate(detail_headers, 1):
            cell = ws_detail.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 写入商品明细数据
        detail_row = 2
        total_detail_count = 0
        
        for stockout in all_stockouts:
            stockout_no = stockout.get('stockout_no', '')
            src_order_no = stockout.get('src_order_no', '')
            
            # 获取商品明细
            goods_detail = stockout.get('goods_detail', [])
            if isinstance(goods_detail, list) and goods_detail:
                for detail in goods_detail:
                    ws_detail.cell(row=detail_row, column=1, value=stockout_no)
                    ws_detail.cell(row=detail_row, column=2, value=src_order_no)
                    ws_detail.cell(row=detail_row, column=3, value=detail.get('goods_no', ''))      # 货品编号
                    ws_detail.cell(row=detail_row, column=4, value=detail.get('spec_no', ''))       # 商品编号
                    ws_detail.cell(row=detail_row, column=5, value=detail.get('spec_id', ''))       # 规格编号
                    ws_detail.cell(row=detail_row, column=6, value=detail.get('goods_name', ''))    # 商品名称
                    ws_detail.cell(row=detail_row, column=7, value=detail.get('spec_name', ''))     # 规格名称
                    ws_detail.cell(row=detail_row, column=8, value=detail.get('goods_count', ''))   # 商品数量
                    ws_detail.cell(row=detail_row, column=9, value=detail.get('sell_price', ''))    # 单价
                    ws_detail.cell(row=detail_row, column=10, value=detail.get('total_amount', '')) # 总金额
                    ws_detail.cell(row=detail_row, column=11, value=detail.get('cost_price', ''))   # 成本价
                    ws_detail.cell(row=detail_row, column=12, value=detail.get('weight', ''))       # 重量
                    ws_detail.cell(row=detail_row, column=13, value=detail.get('barcode', ''))      # 条码
                    ws_detail.cell(row=detail_row, column=14, value=detail.get('brand_no', ''))     # 品牌编号
                    ws_detail.cell(row=detail_row, column=15, value=detail.get('brand_name', ''))   # 品牌名称
                    ws_detail.cell(row=detail_row, column=16, value=detail.get('class_name', ''))   # 分类名称
                    ws_detail.cell(row=detail_row, column=17, value='是' if detail.get('gift_type') == 1 else '否')  # 是否赠品
                    detail_row += 1
                    total_detail_count += 1
            else:
                # 如果没有明细，至少记录一行基本信息
                ws_detail.cell(row=detail_row, column=1, value=stockout_no)
                ws_detail.cell(row=detail_row, column=2, value=src_order_no)
                ws_detail.cell(row=detail_row, column=3, value='无明细数据')
                ws_detail.cell(row=detail_row, column=4, value='无明细数据')
                detail_row += 1
        
        # 自动调整明细表列宽
        for column in ws_detail.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_detail.column_dimensions[column_letter].width = adjusted_width
        
        # 保存文件
        wb.save(filename)
        
        # 统计数据
        total_amount = sum(float(s.get('total_amount', 0) or 0) for s in all_stockouts)
        total_goods = sum(float(s.get('goods_count', 0) or 0) for s in all_stockouts)
        
        print(f"✅ 导出成功！")
        print(f"📊 总计导出: {len(all_stockouts)} 条已发货订单")
        print(f"📋 商品明细: {total_detail_count} 条")
        print(f"💰 订单总金额: {total_amount:.2f}")
        print(f"📦 商品总数量: {total_goods:.0f}")
        print(f"📁 文件路径: {filename}")
        
        print(f"\n🎉 强制获取销售出库明细单导出完成！")
        
    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    export_all_stockout_force()
