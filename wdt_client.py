"""
旺店通WMS API客户端
"""
import requests
import json
import time
import logging
from typing import Dict, Any, Optional, List
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from config import WDTConfig
from wdt_signature import WDTSignature


class WDTAPIException(Exception):
    """旺店通API异常类"""
    
    def __init__(self, status_code: int, message: str, response_data: Optional[Dict] = None):
        self.status_code = status_code
        self.message = message
        self.response_data = response_data
        super().__init__(f"API Error {status_code}: {message}")


class WDTClient:
    """旺店通WMS API客户端"""
    
    def __init__(self, sid: str = None, app_key: str = None, app_secret: str = None, 
                 api_url: str = None, timeout: int = None):
        """
        初始化客户端
        
        Args:
            sid: 卖家标识
            app_key: 应用Key
            app_secret: 应用密钥
            api_url: API地址
            timeout: 请求超时时间
        """
        self.sid = sid or WDTConfig.SID
        self.app_key = app_key or WDTConfig.APP_KEY
        self.app_secret = app_secret or WDTConfig.APP_SECRET
        self.api_url = api_url or WDTConfig.API_URL
        self.timeout = timeout or WDTConfig.REQUEST_TIMEOUT
        
        # 验证配置
        self._validate_config()
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 创建会话
        self.session = self._create_session()
    
    def _validate_config(self):
        """验证配置"""
        required_fields = {
            'sid': self.sid,
            'app_key': self.app_key,
            'app_secret': self.app_secret,
            'api_url': self.api_url
        }
        
        missing_fields = [field for field, value in required_fields.items() if not value]
        if missing_fields:
            raise ValueError(f"缺少必要的配置项: {', '.join(missing_fields)}")
    
    def _create_session(self) -> requests.Session:
        """创建HTTP会话"""
        session = requests.Session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=WDTConfig.MAX_RETRIES,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置默认headers
        session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'WDT-Python-Client/1.0'
        })
        
        return session
    
    def _make_request(self, method: str, params: Dict[str, Any] = None, 
                     pager: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        发送API请求
        
        Args:
            method: API方法名
            params: 业务参数
            pager: 分页参数
            
        Returns:
            API响应数据
        """
        # 获取secret和salt
        if ':' in self.app_secret:
            secret, salt = self.app_secret.split(':', 1)
        else:
            secret, salt = self.app_secret, ''

        # 构建请求参数
        request_params = WDTSignature.build_request_params(
            method=method,
            params=params,
            pager=pager,
            sid=self.sid,
            app_key=self.app_key,
            app_secret=secret,
            salt=salt
        )
        
        self.logger.debug(f"请求参数: {json.dumps(request_params, ensure_ascii=False, indent=2)}")
        
        try:
            # 发送POST请求 - 使用form-data格式
            # 需要将复杂参数转换为JSON字符串
            form_data = {}
            for key, value in request_params.items():
                if isinstance(value, (dict, list)):
                    form_data[key] = json.dumps(value, ensure_ascii=False)
                else:
                    form_data[key] = str(value)

            response = self.session.post(
                self.api_url,
                data=form_data,
                timeout=self.timeout
            )
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析响应
            response_data = response.json()
            
            self.logger.debug(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            # 检查API状态码
            # 旺店通API可能返回不同的响应格式
            if 'flag' in response_data:
                # 新格式: {"flag": "success/failure", "code": "...", "message": "..."}
                flag = response_data.get('flag')
                if flag != 'success':
                    code = response_data.get('code', 'unknown')
                    message = response_data.get('message', '未知错误')
                    raise WDTAPIException(-1, message, response_data)
            else:
                # 旧格式: {"status": 0, "message": "..."}
                status = response_data.get('status', -1)
                if status != 0:
                    message = response_data.get('message', '未知错误')
                    raise WDTAPIException(status, message, response_data)
            
            return response_data
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求异常: {e}")
            raise WDTAPIException(-1, f"请求异常: {e}")
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析异常: {e}")
            raise WDTAPIException(-1, f"JSON解析异常: {e}")
    
    def call_api(self, method: str, params: Dict[str, Any] = None, 
                 page_size: int = None, page_no: int = 0, 
                 calc_total: bool = True) -> Dict[str, Any]:
        """
        调用API
        
        Args:
            method: API方法名
            params: 业务参数
            page_size: 分页大小
            page_no: 页码（从0开始）
            calc_total: 是否计算总数
            
        Returns:
            API响应数据
        """
        # 构建分页参数
        pager = None
        if page_size is not None:
            pager = {
                'page_size': page_size,
                'page_no': page_no
            }
            if calc_total:
                pager['calc_total'] = 1
        
        return self._make_request(method, params, pager)
    
    def call_api_all_pages(self, method: str, params: Dict[str, Any] = None,
                          page_size: int = None, max_pages: int = None) -> List[Dict[str, Any]]:
        """
        获取所有分页数据
        
        Args:
            method: API方法名
            params: 业务参数
            page_size: 分页大小
            max_pages: 最大页数限制
            
        Returns:
            所有页面的数据列表
        """
        page_size = page_size or WDTConfig.DEFAULT_PAGE_SIZE
        all_data = []
        page_no = 0
        
        while True:
            if max_pages and page_no >= max_pages:
                break
                
            response = self.call_api(method, params, page_size, page_no, calc_total=True)
            data = response.get('data', {})
            
            # 获取当前页数据
            if 'order' in data:
                current_page_data = data['order']
            elif 'list' in data:
                current_page_data = data['list']
            else:
                current_page_data = data.get('data', [])
            
            if not current_page_data:
                break
                
            all_data.extend(current_page_data)
            
            # 检查是否还有更多数据
            total_count = data.get('total_count', 0)
            if len(all_data) >= total_count:
                break
                
            page_no += 1
            
            # 添加延迟避免频率限制
            time.sleep(0.1)
        
        return all_data
